# Google Cloud Console 设置指南

## 🎯 为 jhun.edu.kg 域名设置 Google Admin SDK

### 第一步：创建 Google Cloud Console 项目

1. **访问 Google Cloud Console**
   - 打开 https://console.cloud.google.com/
   - 使用您的 Google Workspace 管理员账号登录

2. **创建新项目**
   - 点击项目选择器
   - 点击"新建项目"
   - 项目名称：`jhun-edu-kg-admin-tool`
   - 组织：选择 `jhun.edu.kg`
   - 点击"创建"

### 第二步：启用 Admin SDK API

1. **启用 API**
   - 在项目中，转到"API 和服务" > "库"
   - 搜索 "Admin SDK API"
   - 点击 "Admin SDK API"
   - 点击"启用"

2. **启用其他必要的 API**
   - Google+ API（用于用户信息）
   - People API（用于联系人信息）

### 第三步：配置 OAuth 2.0 客户端

1. **创建 OAuth 2.0 客户端 ID**
   - 转到"API 和服务" > "凭据"
   - 点击"创建凭据" > "OAuth 客户端 ID"

2. **配置同意屏幕**
   - 如果提示，先配置 OAuth 同意屏幕
   - 用户类型：内部（仅限 jhun.edu.kg 域用户）
   - 应用名称：`JHUN Admin Console 用户管理工具`
   - 用户支持电子邮件：您的管理员邮箱
   - 授权域：`jhun.edu.kg`
   - 开发者联系信息：您的管理员邮箱

3. **创建 OAuth 客户端**
   - 应用类型：Web 应用
   - 名称：`JHUN Admin Console Client`
   - 授权的重定向 URI：
     - `http://localhost:3000/api/auth/callback/google`
     - `https://your-domain.com/api/auth/callback/google`（生产环境）

4. **下载客户端配置**
   - 下载 JSON 文件
   - 重命名为 `google-credentials.json`
   - 保存到项目根目录（不要提交到 Git）

### 第四步：设置管理员权限

1. **Google Workspace Admin Console 设置**
   - 访问 https://admin.google.com/
   - 转到"安全" > "API 控制"
   - 点击"管理域范围的委派"

2. **添加客户端 ID**
   - 客户端 ID：从 OAuth 客户端配置中获取
   - OAuth 范围：
     ```
     https://www.googleapis.com/auth/admin.directory.user
     https://www.googleapis.com/auth/admin.directory.orgunit
     https://www.googleapis.com/auth/admin.directory.group
     ```

### 第五步：环境变量配置

创建 `.env.local` 文件：

```env
# Google OAuth 2.0 配置
GOOGLE_CLIENT_ID=your_client_id_here
GOOGLE_CLIENT_SECRET=your_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:3000/api/auth/callback/google

# Google Workspace 域名
GOOGLE_WORKSPACE_DOMAIN=jhun.edu.kg

# NextAuth 配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_random_secret_here

# 管理员邮箱（可选，用于权限验证）
ADMIN_EMAIL=<EMAIL>
```

### 第六步：验证设置

1. **测试 API 访问**
   - 使用 Google API Explorer 测试
   - 确认可以访问用户列表

2. **权限验证**
   - 确认管理员账号有足够权限
   - 测试用户创建权限

## 🔐 安全注意事项

1. **凭据安全**
   - 不要将 `google-credentials.json` 提交到版本控制
   - 使用环境变量存储敏感信息
   - 定期轮换客户端密钥

2. **权限最小化**
   - 只申请必要的 API 权限
   - 限制应用访问范围

3. **审计日志**
   - 启用 Google Workspace 审计日志
   - 监控 API 使用情况

## 📝 下一步

完成上述设置后，我们将：
1. 实现 OAuth 2.0 认证流程
2. 集成真实的 Google Admin SDK
3. 替换模拟 API 调用
4. 添加辅助电子邮件支持

## 🆘 常见问题

**Q: 如何获取管理员权限？**
A: 您需要是 jhun.edu.kg 域的超级管理员才能进行这些设置。

**Q: API 配额限制是什么？**
A: Google Admin SDK 有每日配额限制，通常足够中小型组织使用。

**Q: 如何处理多因素认证？**
A: OAuth 2.0 流程会自动处理 MFA，用户需要完成所有认证步骤。
