{"name": "google-admin-invite", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@googleapis/admin": "^23.5.0", "@hookform/resolvers": "^5.0.1", "googleapis": "^144.0.0", "lucide-react": "^0.511.0", "next": "15.3.3", "next-auth": "^4.24.10", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "zod": "^3.25.49", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}