<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Admin Console 用户批量添加工具 - 改进演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .password-strength-weak { background-color: #ef4444; }
        .password-strength-medium { background-color: #f59e0b; }
        .password-strength-strong { background-color: #3b82f6; }
        .password-strength-very-strong { background-color: #10b981; }
        
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: .5; }
        }
        
        .progress-bar {
            transition: width 0.3s ease-out;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-xl font-semibold text-gray-900">
                            Google Admin Console
                        </h1>
                    </div>
                    <div class="text-sm text-gray-500">
                        用户管理 / 批量添加用户
                    </div>
                </div>
            </div>
        </header>

        <main class="py-8">
            <div class="max-w-6xl mx-auto p-6">
                <!-- 改进说明 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                    <h2 class="text-lg font-semibold text-blue-900 mb-4">🎉 项目改进完成！</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                        <div>
                            <h3 class="font-medium mb-2">✅ 已完成的改进：</h3>
                            <ul class="space-y-1">
                                <li>• 表单自动保存功能</li>
                                <li>• 密码强度可视化指示器</li>
                                <li>• 增强的加载状态显示</li>
                                <li>• 自动保存状态提示</li>
                                <li>• 改进的错误处理</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-medium mb-2">🚀 下一步计划：</h3>
                            <ul class="space-y-1">
                                <li>• CSV 批量导入功能</li>
                                <li>• 用户管理界面</li>
                                <li>• 状态管理重构</li>
                                <li>• 性能优化</li>
                                <li>• 测试覆盖</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 自动保存指示器演示 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start gap-3">
                        <svg class="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-blue-900 mb-1">
                                发现未完成的表单数据
                            </h4>
                            <p class="text-sm text-blue-800 mb-3">
                                我们发现您有一个自动保存的表单草稿，包含 3 个用户，保存于 5分钟前。是否要恢复这些数据？
                            </p>
                            <div class="flex gap-2">
                                <button class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                    恢复数据
                                </button>
                                <button class="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300">
                                    忽略
                                </button>
                                <button class="px-3 py-1 text-red-600 text-sm hover:bg-red-50 rounded">
                                    删除草稿
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载进度演示 -->
                <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm mb-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <svg class="h-5 w-5 text-blue-600 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900">正在创建用户</h3>
                        </div>
                        <button class="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50">
                            取消
                        </button>
                    </div>

                    <div class="mb-4">
                        <p class="text-sm text-gray-600">正在创建用户账户...</p>
                    </div>

                    <div class="mb-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">
                                进度: 7/10 用户
                            </span>
                            <span class="text-sm text-gray-500">70%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full progress-bar" style="width: 70%"></div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-500">总计</p>
                                <p class="text-sm font-medium">10</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-500">成功</p>
                                <p class="text-sm font-medium text-green-600">6</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-500">失败</p>
                                <p class="text-sm font-medium text-red-600">1</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-500">已用时</p>
                                <p class="text-sm font-medium">2分30秒</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm text-blue-800">
                                预计剩余时间: 1分10秒
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 密码强度指示器演示 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">密码强度指示器演示</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">密码示例 1: "123"</label>
                            <input type="password" value="123" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                            <div class="mt-2 space-y-2">
                                <div class="flex items-center gap-2">
                                    <span class="text-sm text-gray-600">密码强度:</span>
                                    <div class="flex-1 bg-gray-200 rounded-full h-2">
                                        <div class="password-strength-weak h-2 rounded-full" style="width: 20%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-red-600">弱</span>
                                </div>
                                <div class="text-xs text-red-600 bg-red-50 p-2 rounded">
                                    💡 建议：使用更长的密码并包含大小写字母、数字和特殊字符
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">密码示例 2: "SecurePass123!"</label>
                            <input type="password" value="SecurePass123!" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                            <div class="mt-2 space-y-2">
                                <div class="flex items-center gap-2">
                                    <span class="text-sm text-gray-600">密码强度:</span>
                                    <div class="flex-1 bg-gray-200 rounded-full h-2">
                                        <div class="password-strength-very-strong h-2 rounded-full" style="width: 100%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-green-600">很强</span>
                                </div>
                                <div class="text-xs text-green-600 bg-green-50 p-2 rounded">
                                    ✅ 优秀！这是一个非常安全的密码
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 自动保存状态指示器 -->
                <div class="mt-4 flex items-center gap-2 text-xs text-gray-500">
                    <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <span>表单数据会自动保存</span>
                    <span>•</span>
                    <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>上次保存: 30秒前</span>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 mt-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="text-center text-sm text-gray-500">
                    <p>Google Admin Console 用户批量添加工具 - 已完成第一阶段改进</p>
                    <p class="mt-1">
                        基于 Next.js、Tailwind CSS 和 Google Admin SDK 构建
                    </p>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
