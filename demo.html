<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Admin Console 用户批量添加工具 - 改进演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .password-strength-weak { background-color: #ef4444; }
        .password-strength-medium { background-color: #f59e0b; }
        .password-strength-strong { background-color: #3b82f6; }
        .password-strength-very-strong { background-color: #10b981; }
        
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: .5; }
        }
        
        .progress-bar {
            transition: width 0.3s ease-out;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-xl font-semibold text-gray-900">
                            Google Admin Console
                        </h1>
                    </div>
                    <div class="text-sm text-gray-500">
                        用户管理 / 批量添加用户
                    </div>
                </div>
            </div>
        </header>

        <main class="py-8">
            <div class="max-w-6xl mx-auto p-6">
                <!-- 改进说明 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                    <h2 class="text-lg font-semibold text-blue-900 mb-4">🎉 第三阶段改进完成！</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800">
                        <div>
                            <h3 class="font-medium mb-2">✅ 第一阶段：</h3>
                            <ul class="space-y-1">
                                <li>• 表单自动保存功能</li>
                                <li>• 密码强度可视化指示器</li>
                                <li>• 增强的加载状态显示</li>
                                <li>• 自动保存状态提示</li>
                                <li>• 改进的错误处理</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-medium mb-2">🚀 第二阶段：</h3>
                            <ul class="space-y-1">
                                <li>• CSV 批量导入系统</li>
                                <li>• 文件拖拽上传功能</li>
                                <li>• 数据预览和编辑界面</li>
                                <li>• 实时数据验证</li>
                                <li>• 导入进度跟踪</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-medium mb-2">🎯 第三阶段新增：</h3>
                            <ul class="space-y-1">
                                <li>• 完整用户管理系统</li>
                                <li>• Zustand 状态管理</li>
                                <li>• 高级搜索和筛选</li>
                                <li>• 批量用户操作</li>
                                <li>• 用户详情查看</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 自动保存指示器演示 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start gap-3">
                        <svg class="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-blue-900 mb-1">
                                发现未完成的表单数据
                            </h4>
                            <p class="text-sm text-blue-800 mb-3">
                                我们发现您有一个自动保存的表单草稿，包含 3 个用户，保存于 5分钟前。是否要恢复这些数据？
                            </p>
                            <div class="flex gap-2">
                                <button class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                    恢复数据
                                </button>
                                <button class="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300">
                                    忽略
                                </button>
                                <button class="px-3 py-1 text-red-600 text-sm hover:bg-red-50 rounded">
                                    删除草稿
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载进度演示 -->
                <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm mb-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <svg class="h-5 w-5 text-blue-600 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900">正在创建用户</h3>
                        </div>
                        <button class="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50">
                            取消
                        </button>
                    </div>

                    <div class="mb-4">
                        <p class="text-sm text-gray-600">正在创建用户账户...</p>
                    </div>

                    <div class="mb-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">
                                进度: 7/10 用户
                            </span>
                            <span class="text-sm text-gray-500">70%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full progress-bar" style="width: 70%"></div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-500">总计</p>
                                <p class="text-sm font-medium">10</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-500">成功</p>
                                <p class="text-sm font-medium text-green-600">6</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-500">失败</p>
                                <p class="text-sm font-medium text-red-600">1</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-500">已用时</p>
                                <p class="text-sm font-medium">2分30秒</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm text-blue-800">
                                预计剩余时间: 1分10秒
                            </span>
                        </div>
                    </div>
                </div>

                <!-- CSV 导入功能演示 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">CSV 批量导入功能演示</h3>

                    <!-- 步骤指示器 -->
                    <div class="flex items-center justify-center mb-6">
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 rounded-full border-2 bg-green-500 border-green-500 text-white">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                            </div>
                            <span class="ml-2 text-sm font-medium text-gray-500">上传文件</span>
                            <div class="mx-4 h-0.5 w-8 bg-green-500"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 rounded-full border-2 bg-blue-500 border-blue-500 text-white">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <span class="ml-2 text-sm font-medium text-blue-600">预览数据</span>
                            <div class="mx-4 h-0.5 w-8 bg-gray-300"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 rounded-full border-2 bg-gray-100 border-gray-300 text-gray-500">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="ml-2 text-sm font-medium text-gray-500">确认导入</span>
                        </div>
                    </div>

                    <!-- 数据预览表格 -->
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="w-12 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">#</th>
                                        <th class="w-48 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">邮箱地址 *</th>
                                        <th class="w-24 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">名字 *</th>
                                        <th class="w-24 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">姓氏 *</th>
                                        <th class="w-32 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">密码 *</th>
                                        <th class="w-32 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">组织单位</th>
                                        <th class="w-16 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-3 py-2 text-xs text-gray-500">1</td>
                                        <td class="px-3 py-2">
                                            <div class="group flex items-center justify-between cursor-pointer p-1 rounded hover:bg-gray-50">
                                                <span class="text-xs truncate flex-1"><EMAIL></span>
                                                <svg class="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </div>
                                        </td>
                                        <td class="px-3 py-2">
                                            <div class="group flex items-center justify-between cursor-pointer p-1 rounded hover:bg-gray-50">
                                                <span class="text-xs truncate flex-1">三</span>
                                                <svg class="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </div>
                                        </td>
                                        <td class="px-3 py-2">
                                            <div class="group flex items-center justify-between cursor-pointer p-1 rounded hover:bg-gray-50">
                                                <span class="text-xs truncate flex-1">张</span>
                                                <svg class="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </div>
                                        </td>
                                        <td class="px-3 py-2">
                                            <div class="group flex items-center justify-between cursor-pointer p-1 rounded hover:bg-gray-50">
                                                <span class="text-xs truncate flex-1">••••••••</span>
                                                <svg class="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </div>
                                        </td>
                                        <td class="px-3 py-2">
                                            <div class="group flex items-center justify-between cursor-pointer p-1 rounded hover:bg-gray-50">
                                                <span class="text-xs truncate flex-1">/</span>
                                                <svg class="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </div>
                                        </td>
                                        <td class="px-3 py-2">
                                            <button class="p-1 text-red-600 hover:bg-red-50 rounded" title="删除行">
                                                <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr class="bg-red-50">
                                        <td class="px-3 py-2 text-xs text-gray-500">
                                            <div class="flex items-center gap-1">
                                                2
                                                <svg class="h-3 w-3 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                </svg>
                                            </div>
                                        </td>
                                        <td class="px-3 py-2">
                                            <div class="group flex items-center justify-between cursor-pointer p-1 rounded bg-red-50 text-red-800">
                                                <span class="text-xs truncate flex-1">invalid-email</span>
                                                <svg class="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </div>
                                            <div class="text-xs text-red-600 mt-1">请输入有效的邮箱地址</div>
                                        </td>
                                        <td class="px-3 py-2">
                                            <div class="group flex items-center justify-between cursor-pointer p-1 rounded hover:bg-gray-50">
                                                <span class="text-xs truncate flex-1">四</span>
                                                <svg class="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </div>
                                        </td>
                                        <td class="px-3 py-2">
                                            <div class="group flex items-center justify-between cursor-pointer p-1 rounded hover:bg-gray-50">
                                                <span class="text-xs truncate flex-1">李</span>
                                                <svg class="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </div>
                                        </td>
                                        <td class="px-3 py-2">
                                            <div class="group flex items-center justify-between cursor-pointer p-1 rounded bg-red-50 text-red-800">
                                                <span class="text-xs truncate flex-1">••••••••</span>
                                                <svg class="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </div>
                                            <div class="text-xs text-red-600 mt-1">密码必须包含至少一个小写字母、一个大写字母和一个数字</div>
                                        </td>
                                        <td class="px-3 py-2">
                                            <div class="group flex items-center justify-between cursor-pointer p-1 rounded hover:bg-gray-50">
                                                <span class="text-xs truncate flex-1">/工程部</span>
                                                <svg class="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </div>
                                        </td>
                                        <td class="px-3 py-2">
                                            <button class="p-1 text-red-600 hover:bg-red-50 rounded" title="删除行">
                                                <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 验证结果摘要 -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">验证结果</h4>
                        <div class="grid grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">总行数:</span>
                                <span class="ml-2 font-medium">2</span>
                            </div>
                            <div>
                                <span class="text-green-600">有效行数:</span>
                                <span class="ml-2 font-medium text-green-600">1</span>
                            </div>
                            <div>
                                <span class="text-red-600">错误行数:</span>
                                <span class="ml-2 font-medium text-red-600">1</span>
                            </div>
                        </div>
                    </div>

                    <!-- 操作提示 -->
                    <div class="mt-4 text-xs text-gray-500 bg-gray-50 p-3 rounded">
                        💡 提示：点击任意单元格可以编辑内容，按 Enter 保存，按 Esc 取消
                    </div>
                </div>

                <!-- 用户管理系统演示 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">用户管理系统演示</h3>

                    <!-- 统计信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-blue-600">总用户数</p>
                                    <p class="text-2xl font-semibold text-blue-900">50</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-green-50 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-8 w-8 bg-green-600 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm font-bold">✓</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-green-600">活跃用户</p>
                                    <p class="text-2xl font-semibold text-green-900">45</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-yellow-50 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-8 w-8 bg-yellow-600 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm font-bold">⏸</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-yellow-600">已暂停</p>
                                    <p class="text-2xl font-semibold text-yellow-900">5</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-purple-50 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-8 w-8 bg-purple-600 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm font-bold">✓</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-purple-600">已选择</p>
                                    <p class="text-2xl font-semibold text-purple-900">3</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索和筛选 -->
                    <div class="space-y-4 mb-6">
                        <div class="flex items-center gap-3">
                            <div class="flex-1 relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <input
                                    type="text"
                                    placeholder="搜索用户（邮箱、姓名）..."
                                    class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    value="张三"
                                />
                            </div>
                            <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">搜索</button>
                            <button class="relative px-4 py-2 bg-blue-50 border border-blue-300 text-blue-700 rounded-md">
                                <div class="flex items-center gap-2">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                                    </svg>
                                    <span>筛选</span>
                                </div>
                                <span class="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">2</span>
                            </button>
                        </div>

                        <!-- 活跃筛选器标签 -->
                        <div class="flex items-center gap-2 flex-wrap">
                            <span class="text-sm text-gray-600">活跃筛选器:</span>
                            <span class="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                搜索: 张三
                                <button class="hover:bg-blue-200 rounded-full p-0.5">
                                    <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </span>
                            <span class="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                组织: 工程部
                                <button class="hover:bg-green-200 rounded-full p-0.5">
                                    <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </span>
                        </div>
                    </div>

                    <!-- 批量操作栏 -->
                    <div class="bg-white border border-gray-200 rounded-lg p-4 mb-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <span class="text-sm font-medium text-gray-900">已选择 3 个用户</span>
                                <button class="text-sm text-gray-500 hover:text-gray-700 underline">清除选择</button>
                            </div>
                            <div class="flex items-center gap-2">
                                <button class="flex items-center gap-2 px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    启用
                                </button>
                                <button class="flex items-center gap-2 px-3 py-2 text-sm bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    暂停
                                </button>
                                <select class="px-2 py-2 text-sm border border-gray-300 rounded-md">
                                    <option>根组织单位</option>
                                    <option>工程部</option>
                                    <option>销售部</option>
                                </select>
                                <button class="flex items-center gap-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    移动
                                </button>
                                <button class="flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    导出
                                </button>
                                <button class="flex items-center gap-2 px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 用户列表 -->
                    <div class="bg-white border border-gray-200 rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-4">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <span class="ml-2 text-sm text-gray-700">已选择 3 个用户</span>
                                    </label>
                                </div>
                                <div class="text-sm text-gray-500">显示 1-5 / 共 50 个用户</div>
                            </div>
                        </div>

                        <div class="divide-y divide-gray-200">
                            <!-- 用户行 1 -->
                            <div class="px-6 py-4 bg-blue-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <div class="flex-shrink-0">
                                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center gap-2">
                                                <p class="text-sm font-medium text-gray-900 truncate">张三</p>
                                            </div>
                                            <div class="flex items-center gap-4 mt-1 text-xs text-gray-500">
                                                <div class="flex items-center gap-1">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                    </svg>
                                                    <span class="truncate"><EMAIL></span>
                                                </div>
                                                <div class="flex items-center gap-1">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                                    </svg>
                                                    <span>/工程部</span>
                                                </div>
                                                <div class="flex items-center gap-1">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4m-6 0h6m-6 0V7a1 1 0 00-1 1v9a2 2 0 002 2h4a2 2 0 002-2V8a1 1 0 00-1-1V7"></path>
                                                    </svg>
                                                    <span>最后登录: 2天前</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="text-xs text-gray-500">创建于 2024-01-15</div>
                                        <button class="p-1 rounded-full hover:bg-gray-200">
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 用户行 2 -->
                            <div class="px-6 py-4 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <div class="flex-shrink-0">
                                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center gap-2">
                                                <p class="text-sm font-medium text-gray-900 truncate">李四</p>
                                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">已暂停</span>
                                            </div>
                                            <div class="flex items-center gap-4 mt-1 text-xs text-gray-500">
                                                <div class="flex items-center gap-1">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                    </svg>
                                                    <span class="truncate"><EMAIL></span>
                                                </div>
                                                <div class="flex items-center gap-1">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                                    </svg>
                                                    <span>/销售部</span>
                                                </div>
                                                <div class="flex items-center gap-1">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4m-6 0h6m-6 0V7a1 1 0 00-1 1v9a2 2 0 002 2h4a2 2 0 002-2V8a1 1 0 00-1-1V7"></path>
                                                    </svg>
                                                    <span>最后登录: 从未登录</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="text-xs text-gray-500">创建于 2024-01-10</div>
                                        <button class="p-1 rounded-full hover:bg-gray-200">
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <div class="px-6 py-4 border-t border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-700">第 1 页，共 10 页</div>
                                <div class="flex items-center gap-2">
                                    <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50" disabled>上一页</button>
                                    <button class="px-3 py-1 text-sm bg-blue-600 text-white border border-blue-600 rounded">1</button>
                                    <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">2</button>
                                    <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">3</button>
                                    <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">下一页</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 密码强度指示器演示 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">密码强度指示器演示</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">密码示例 1: "123"</label>
                            <input type="password" value="123" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                            <div class="mt-2 space-y-2">
                                <div class="flex items-center gap-2">
                                    <span class="text-sm text-gray-600">密码强度:</span>
                                    <div class="flex-1 bg-gray-200 rounded-full h-2">
                                        <div class="password-strength-weak h-2 rounded-full" style="width: 20%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-red-600">弱</span>
                                </div>
                                <div class="text-xs text-red-600 bg-red-50 p-2 rounded">
                                    💡 建议：使用更长的密码并包含大小写字母、数字和特殊字符
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">密码示例 2: "SecurePass123!"</label>
                            <input type="password" value="SecurePass123!" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                            <div class="mt-2 space-y-2">
                                <div class="flex items-center gap-2">
                                    <span class="text-sm text-gray-600">密码强度:</span>
                                    <div class="flex-1 bg-gray-200 rounded-full h-2">
                                        <div class="password-strength-very-strong h-2 rounded-full" style="width: 100%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-green-600">很强</span>
                                </div>
                                <div class="text-xs text-green-600 bg-green-50 p-2 rounded">
                                    ✅ 优秀！这是一个非常安全的密码
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 自动保存状态指示器 -->
                <div class="mt-4 flex items-center gap-2 text-xs text-gray-500">
                    <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <span>表单数据会自动保存</span>
                    <span>•</span>
                    <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>上次保存: 30秒前</span>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 mt-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="text-center text-sm text-gray-500">
                    <p>Google Admin Console 用户批量添加工具 - 已完成第一阶段改进</p>
                    <p class="mt-1">
                        基于 Next.js、Tailwind CSS 和 Google Admin SDK 构建
                    </p>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
