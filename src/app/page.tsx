'use client';

import { useState, useEffect } from 'react';
import { useSession, signIn, signOut } from 'next-auth/react';
import { LogOut, User } from 'lucide-react';
import UserForm from '@/components/UserForm';
import Instructions from '@/components/Instructions';
import CSVImport from '@/components/CSVImport';
import UserManagement from '@/components/UserManagement';
import { BulkUserFormData, UserFormData } from '@/lib/validations';
import { UserCreationResult } from '@/types/user';

export default function Home() {
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'form' | 'csv' | 'manage'>('form');

  // 如果未认证，重定向到登录页面
  useEffect(() => {
    if (status === 'unauthenticated') {
      signIn();
    }
  }, [status]);

  // 显示加载状态
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">正在加载...</p>
        </div>
      </div>
    );
  }

  // 如果未认证，显示登录提示
  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">请先登录</h1>
          <button
            onClick={() => signIn()}
            className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 font-medium"
          >
            <span className="leading-none">登录</span>
          </button>
        </div>
      </div>
    );
  }

  // 检查管理员权限
  if (!session.isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">权限不足</h1>
          <p className="text-gray-600 mb-6">
            您需要 jhun.edu.kg 域的管理员权限才能使用此工具。
          </p>
          <button
            onClick={() => signOut()}
            className="inline-flex items-center justify-center px-6 py-3 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200 font-medium"
          >
            <span className="leading-none">退出登录</span>
          </button>
        </div>
      </div>
    );
  }

  const handleSubmit = async (data: BulkUserFormData): Promise<UserCreationResult[]> => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '请求失败');
      }

      const result = await response.json();
      return result.results;
    } catch (error) {
      console.error('提交失败:', error);
      // 返回错误结果
      return data.users.map(user => ({
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        email: user.primaryEmail,
      }));
    } finally {
      setIsLoading(false);
    }
  };

  // 处理 CSV 导入
  const handleCSVImport = async (users: UserFormData[]) => {
    const bulkData: BulkUserFormData = { users };
    await handleSubmit(bulkData);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                JHUN Admin Console
              </h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <User className="h-4 w-4" />
                <span>{session.user?.name || session.user?.email}</span>
              </div>
              <button
                onClick={() => signOut()}
                className="inline-flex items-center justify-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
              >
                <LogOut className="h-4 w-4 flex-shrink-0" />
                <span className="leading-none">退出</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="py-8">
        <Instructions />

        {/* 标签页导航 */}
        <div className="max-w-6xl mx-auto px-6 mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('form')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'form'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                手动添加
              </button>
              <button
                onClick={() => setActiveTab('csv')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'csv'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                CSV 批量导入
              </button>
              <button
                onClick={() => setActiveTab('manage')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'manage'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                用户管理
              </button>
            </nav>
          </div>
        </div>

        {/* 内容区域 */}
        {activeTab === 'form' && (
          <UserForm onSubmit={handleSubmit} isLoading={isLoading} />
        )}

        {activeTab === 'csv' && (
          <div className="max-w-6xl mx-auto px-6">
            <CSVImport onImport={handleCSVImport} isLoading={isLoading} />
          </div>
        )}

        {activeTab === 'manage' && (
          <div className="max-w-6xl mx-auto px-6">
            <UserManagement onCreateUser={() => setActiveTab('form')} />
          </div>
        )}
      </main>

      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-gray-500">
            <p>Google Admin Console 用户批量添加工具</p>
            <p className="mt-1">
              基于 Next.js、Tailwind CSS 和 Google Admin SDK 构建
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
