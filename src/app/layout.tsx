import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import Providers from "@/components/Providers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "JHUN Admin Console - 用户批量添加",
  description: "基于 Next.js、Tailwind CSS 和 Google Admin SDK 构建的 jhun.edu.kg Google Workspace 用户批量添加管理工具",
  keywords: ["Google Admin", "用户管理", "批量添加", "Google Workspace", "Next.js", "jhun.edu.kg"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
