'use client';

import { useSearchParams } from 'next/navigation';
import { AlertTriangle, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function AuthError() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');

  const getErrorMessage = (error: string | null) => {
    switch (error) {
      case 'Configuration':
        return {
          title: '配置错误',
          message: 'OAuth 配置有误，请联系系统管理员。',
          details: '可能是客户端 ID 或密钥配置不正确。',
        };
      case 'AccessDenied':
        return {
          title: '访问被拒绝',
          message: '您没有权限访问此应用。',
          details: '请确认您使用的是 jhun.edu.kg 域的管理员账号。',
        };
      case 'Verification':
        return {
          title: '验证失败',
          message: '无法验证您的身份。',
          details: '请重试登录，如果问题持续存在，请联系管理员。',
        };
      case 'Default':
      default:
        return {
          title: '登录失败',
          message: '登录过程中发生错误。',
          details: '请重试登录，如果问题持续存在，请联系系统管理员。',
        };
    }
  };

  const errorInfo = getErrorMessage(error);

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* 错误图标和标题 */}
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-red-600 rounded-full flex items-center justify-center">
            <AlertTriangle className="h-8 w-8 text-white" />
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            {errorInfo.title}
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            JHUN Admin Console 登录错误
          </p>
        </div>

        {/* 错误详情 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="space-y-4">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-5 w-5 text-red-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    {errorInfo.message}
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{errorInfo.details}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 错误代码 */}
            {error && (
              <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
                <p className="text-xs text-gray-600">
                  错误代码: <code className="font-mono">{error}</code>
                </p>
              </div>
            )}
          </div>
        </div>

        {/* 解决方案 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">解决方案</h3>
          <div className="space-y-3 text-sm text-gray-700">
            <div className="flex items-start gap-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                1
              </span>
              <p>确认您使用的是 <strong>jhun.edu.kg</strong> 域的 Google 账号</p>
            </div>
            <div className="flex items-start gap-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                2
              </span>
              <p>确认您的账号具有 <strong>超级管理员</strong> 或 <strong>用户管理</strong> 权限</p>
            </div>
            <div className="flex items-start gap-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                3
              </span>
              <p>清除浏览器缓存和 Cookie 后重试</p>
            </div>
            <div className="flex items-start gap-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                4
              </span>
              <p>如果问题持续存在，请联系 IT 管理员</p>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="space-y-3">
          <Link
            href="/auth/signin"
            className="w-full inline-flex items-center justify-center gap-2 py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <span className="leading-none">重新登录</span>
          </Link>

          <Link
            href="/"
            className="w-full inline-flex items-center justify-center gap-2 py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowLeft className="h-4 w-4 flex-shrink-0" />
            <span className="leading-none">返回首页</span>
          </Link>
        </div>

        {/* 联系信息 */}
        <div className="text-center text-xs text-gray-500">
          <p>需要帮助？请联系 IT 支持</p>
          <p className="mt-1">
            邮箱: <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-500"><EMAIL></a>
          </p>
        </div>
      </div>
    </div>
  );
}
