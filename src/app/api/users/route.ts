import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { bulkUserFormSchema } from '@/lib/validations';
import { createUsers, getUsers } from '@/lib/google-admin-oauth';

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions);

    if (!session || !session.accessToken) {
      return NextResponse.json(
        { error: '未认证或访问令牌无效，请重新登录' },
        { status: 401 }
      );
    }

    if (!session.isAdmin) {
      return NextResponse.json(
        { error: '权限不足，需要管理员权限' },
        { status: 403 }
      );
    }

    const body = await request.json();

    // 验证请求数据
    const validationResult = bulkUserFormSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: '请求数据验证失败',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { users } = validationResult.data;

    console.log(`收到创建用户请求，用户数量: ${users.length}`);
    console.log('用户邮箱列表:', users.map(u => u.primaryEmail));

    // 调用 Google Admin SDK 创建用户
    const results = await createUsers(users, session.accessToken);

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    console.log(`用户创建完成 - 成功: ${successCount}, 失败: ${failureCount}`);

    return NextResponse.json({
      success: true,
      results,
      summary: {
        total: results.length,
        success: successCount,
        failure: failureCount,
      },
      message: `处理完成：${successCount} 个用户创建成功，${failureCount} 个失败`,
    });
  } catch (error) {
    console.error('API 错误:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions);

    if (!session || !session.accessToken) {
      return NextResponse.json(
        { error: '未认证或访问令牌无效，请重新登录' },
        { status: 401 }
      );
    }

    if (!session.isAdmin) {
      return NextResponse.json(
        { error: '权限不足，需要管理员权限' },
        { status: 403 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const maxResults = parseInt(searchParams.get('maxResults') || '100');
    const pageToken = searchParams.get('pageToken') || undefined;
    const query = searchParams.get('query') || undefined;
    const orderBy = searchParams.get('orderBy') || undefined;

    console.log('获取用户列表请求:', { maxResults, pageToken, query, orderBy });

    // 调用 Google Admin SDK 获取用户列表
    const result = await getUsers(session.accessToken, {
      maxResults,
      pageToken,
      query,
      orderBy,
    });

    console.log(`获取到 ${result.users.length} 个用户`);

    return NextResponse.json({
      success: true,
      users: result.users,
      nextPageToken: result.nextPageToken,
      count: result.users.length,
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);

    return NextResponse.json(
      {
        error: '获取用户列表失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
