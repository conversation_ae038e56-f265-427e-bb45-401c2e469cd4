'use client';

import { useState, useCallback } from 'react';
import { FileText, Upload, CheckCircle, AlertCircle } from 'lucide-react';
import { UserFormData } from '@/lib/validations';
import { parseCSV, readFileAsText } from '@/lib/csv-parser';
import CSVUploader from './CSVUploader';
import CSVPreview from './CSVPreview';
import CSVValidator, { useCSVValidation } from './CSVValidator';

interface CSVImportProps {
  onImport: (data: UserFormData[]) => void;
  isLoading?: boolean;
  className?: string;
}

type ImportStep = 'upload' | 'preview' | 'confirm';

export default function CSVImport({ 
  onImport, 
  isLoading = false, 
  className = '' 
}: CSVImportProps) {
  const [currentStep, setCurrentStep] = useState<ImportStep>('upload');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<UserFormData[]>([]);
  const [parseErrors, setParseErrors] = useState<string[]>([]);
  const [parseWarnings, setParseWarnings] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  // 使用验证 hook
  const validationResult = useCSVValidation(csvData);

  // 处理文件选择
  const handleFileSelect = useCallback(async (file: File) => {
    setSelectedFile(file);
    setIsProcessing(true);
    setParseErrors([]);
    setParseWarnings([]);

    try {
      const fileContent = await readFileAsText(file);
      const parseResult = parseCSV(fileContent);
      
      setCsvData(parseResult.data);
      setParseErrors(parseResult.errors);
      setParseWarnings(parseResult.warnings);

      if (parseResult.errors.length === 0 && parseResult.data.length > 0) {
        setCurrentStep('preview');
      }
    } catch (error) {
      setParseErrors([`文件处理失败: ${error instanceof Error ? error.message : '未知错误'}`]);
    } finally {
      setIsProcessing(false);
    }
  }, []);

  // 处理数据变更
  const handleDataChange = useCallback((newData: UserFormData[]) => {
    setCsvData(newData);
  }, []);

  // 确认导入
  const handleConfirmImport = useCallback(() => {
    if (validationResult.isValid) {
      onImport(csvData);
    }
  }, [csvData, validationResult.isValid, onImport]);

  // 重新开始
  const handleReset = useCallback(() => {
    setCurrentStep('upload');
    setSelectedFile(null);
    setCsvData([]);
    setParseErrors([]);
    setParseWarnings([]);
  }, []);

  // 渲染步骤指示器
  const renderStepIndicator = () => {
    const steps = [
      { key: 'upload', label: '上传文件', icon: Upload },
      { key: 'preview', label: '预览数据', icon: FileText },
      { key: 'confirm', label: '确认导入', icon: CheckCircle },
    ];

    return (
      <div className="flex items-center justify-center mb-6">
        {steps.map((step, index) => {
          const isActive = step.key === currentStep;
          const isCompleted = steps.findIndex(s => s.key === currentStep) > index;
          const Icon = step.icon;

          return (
            <div key={step.key} className="flex items-center">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  isCompleted
                    ? 'bg-green-500 border-green-500 text-white'
                    : isActive
                    ? 'bg-blue-500 border-blue-500 text-white'
                    : 'bg-gray-100 border-gray-300 text-gray-500'
                }`}
              >
                <Icon className="h-4 w-4" />
              </div>
              <span
                className={`ml-2 text-sm font-medium ${
                  isActive ? 'text-blue-600' : 'text-gray-500'
                }`}
              >
                {step.label}
              </span>
              {index < steps.length - 1 && (
                <div
                  className={`mx-4 h-0.5 w-8 ${
                    isCompleted ? 'bg-green-500' : 'bg-gray-300'
                  }`}
                />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 步骤指示器 */}
      {renderStepIndicator()}

      {/* 错误和警告提示 */}
      {parseErrors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-red-900 mb-2">解析错误</h4>
              <ul className="text-sm text-red-800 space-y-1">
                {parseErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {parseWarnings.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-900 mb-2">警告</h4>
              <ul className="text-sm text-yellow-800 space-y-1">
                {parseWarnings.map((warning, index) => (
                  <li key={index}>• {warning}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {currentStep === 'upload' && (
          <div className="p-6">
            <CSVUploader
              onFileSelect={handleFileSelect}
              isLoading={isProcessing}
            />
          </div>
        )}

        {currentStep === 'preview' && csvData.length > 0 && (
          <div className="p-6">
            <CSVPreview
              data={csvData}
              onDataChange={handleDataChange}
              validationErrors={validationResult.errors}
            />
            
            {/* 验证结果摘要 */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-2">验证结果</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">总行数:</span>
                  <span className="ml-2 font-medium">{validationResult.summary.totalRows}</span>
                </div>
                <div>
                  <span className="text-green-600">有效行数:</span>
                  <span className="ml-2 font-medium text-green-600">{validationResult.summary.validRows}</span>
                </div>
                <div>
                  <span className="text-red-600">错误行数:</span>
                  <span className="ml-2 font-medium text-red-600">{validationResult.summary.errorRows}</span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="mt-6 flex items-center justify-between">
              <button
                onClick={handleReset}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                重新选择文件
              </button>
              <button
                onClick={handleConfirmImport}
                disabled={!validationResult.isValid || isLoading}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? '导入中...' : `导入 ${validationResult.summary.validRows} 个用户`}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 隐藏的验证器组件 */}
      <CSVValidator
        data={csvData}
        onValidationChange={() => {}} // 我们使用 hook 而不是回调
      />
    </div>
  );
}
