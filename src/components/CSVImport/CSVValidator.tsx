'use client';

import { useMemo } from 'react';
import { userFormSchema, UserFormData } from '@/lib/validations';
import { ZodError } from 'zod';

interface ValidationResult {
  isValid: boolean;
  errors: Record<number, Record<string, string>>;
  summary: {
    totalRows: number;
    validRows: number;
    errorRows: number;
    errorTypes: Record<string, number>;
  };
}

interface CSVValidatorProps {
  data: UserFormData[];
  onValidationChange: (result: ValidationResult) => void;
}

export function useCSVValidation(data: UserFormData[]): ValidationResult {
  return useMemo(() => {
    const errors: Record<number, Record<string, string>> = {};
    const errorTypes: Record<string, number> = {};

    data.forEach((row, index) => {
      try {
        // 验证单行数据
        userFormSchema.parse(row);
      } catch (error) {
        if (error instanceof ZodError) {
          const rowErrors: Record<string, string> = {};
          
          error.errors.forEach((err) => {
            const field = err.path[0] as string;
            const message = err.message;
            
            rowErrors[field] = message;
            
            // 统计错误类型
            const errorType = `${field}_${err.code}`;
            errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
          });
          
          if (Object.keys(rowErrors).length > 0) {
            errors[index] = rowErrors;
          }
        }
      }

      // 额外的业务验证
      const businessErrors = validateBusinessRules(row, index, data);
      if (Object.keys(businessErrors).length > 0) {
        errors[index] = { ...errors[index], ...businessErrors };
        
        // 统计业务错误
        Object.keys(businessErrors).forEach(field => {
          const errorType = `${field}_business`;
          errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
        });
      }
    });

    const totalRows = data.length;
    const errorRows = Object.keys(errors).length;
    const validRows = totalRows - errorRows;

    return {
      isValid: errorRows === 0,
      errors,
      summary: {
        totalRows,
        validRows,
        errorRows,
        errorTypes,
      },
    };
  }, [data]);
}

// 业务规则验证
function validateBusinessRules(
  row: UserFormData, 
  index: number, 
  allData: UserFormData[]
): Record<string, string> {
  const errors: Record<string, string> = {};

  // 检查邮箱重复
  const emailCount = allData.filter(
    (item, i) => i !== index && item.primaryEmail === row.primaryEmail
  ).length;
  
  if (emailCount > 0) {
    errors.primaryEmail = '邮箱地址重复';
  }

  // 检查邮箱域名（示例：假设只允许 example.com 域名）
  if (row.primaryEmail && !row.primaryEmail.includes('@')) {
    errors.primaryEmail = '邮箱格式无效';
  }

  // 检查密码复杂度（额外检查）
  if (row.password) {
    if (row.password.length < 8) {
      errors.password = '密码至少需要8个字符';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(row.password)) {
      errors.password = '密码必须包含大小写字母和数字';
    }
  }

  // 检查组织单位路径格式
  if (row.orgUnitPath && !row.orgUnitPath.startsWith('/')) {
    errors.orgUnitPath = '组织单位路径必须以 / 开头';
  }

  return errors;
}

export default function CSVValidator({ data, onValidationChange }: CSVValidatorProps) {
  const validationResult = useCSVValidation(data);

  // 当验证结果改变时通知父组件
  useMemo(() => {
    onValidationChange(validationResult);
  }, [validationResult, onValidationChange]);

  return null; // 这是一个逻辑组件，不渲染任何内容
}
