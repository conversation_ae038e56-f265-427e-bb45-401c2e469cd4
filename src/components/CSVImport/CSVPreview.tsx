'use client';

import { useState, useMemo } from 'react';
import { Edit3, Check, X, AlertTriangle, Users } from 'lucide-react';
import { UserFormData } from '@/lib/validations';

interface CSVPreviewProps {
  data: UserFormData[];
  onDataChange: (data: UserFormData[]) => void;
  validationErrors: Record<number, Record<string, string>>;
  className?: string;
}

interface EditingCell {
  rowIndex: number;
  field: keyof UserFormData;
}

export default function CSVPreview({
  data,
  onDataChange,
  validationErrors,
  className = '',
}: CSVPreviewProps) {
  const [editingCell, setEditingCell] = useState<EditingCell | null>(null);
  const [editValue, setEditValue] = useState('');

  // 计算统计信息
  const stats = useMemo(() => {
    const totalRows = data.length;
    const errorRows = Object.keys(validationErrors).length;
    const validRows = totalRows - errorRows;
    
    return {
      total: totalRows,
      valid: validRows,
      errors: errorRows,
    };
  }, [data, validationErrors]);

  // 字段配置
  const fields = [
    { key: 'primaryEmail' as const, label: '邮箱地址', required: true, width: 'w-48' },
    { key: 'givenName' as const, label: '名字', required: true, width: 'w-24' },
    { key: 'familyName' as const, label: '姓氏', required: true, width: 'w-24' },
    { key: 'password' as const, label: '密码', required: true, width: 'w-32' },
    { key: 'orgUnitPath' as const, label: '组织单位', required: false, width: 'w-32' },
    { key: 'suspended' as const, label: '暂停', required: false, width: 'w-20' },
    { key: 'changePasswordAtNextLogin' as const, label: '强制改密', required: false, width: 'w-24' },
    { key: 'includeInGlobalAddressList' as const, label: '全局列表', required: false, width: 'w-24' },
  ];

  // 开始编辑
  const startEdit = (rowIndex: number, field: keyof UserFormData) => {
    const currentValue = data[rowIndex][field];
    setEditValue(String(currentValue || ''));
    setEditingCell({ rowIndex, field });
  };

  // 保存编辑
  const saveEdit = () => {
    if (!editingCell) return;

    const newData = [...data];
    const { rowIndex, field } = editingCell;
    
    // 处理不同类型的字段
    if (field === 'suspended' || field === 'changePasswordAtNextLogin' || field === 'includeInGlobalAddressList') {
      newData[rowIndex][field] = editValue.toLowerCase() === 'true' || editValue === '1';
    } else {
      newData[rowIndex][field] = editValue;
    }

    onDataChange(newData);
    setEditingCell(null);
    setEditValue('');
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditingCell(null);
    setEditValue('');
  };

  // 删除行
  const deleteRow = (index: number) => {
    const newData = data.filter((_, i) => i !== index);
    onDataChange(newData);
  };

  // 渲染单元格内容
  const renderCellContent = (row: UserFormData, field: keyof UserFormData, rowIndex: number) => {
    const value = row[field];
    const isEditing = editingCell?.rowIndex === rowIndex && editingCell?.field === field;
    const hasError = validationErrors[rowIndex]?.[field];

    if (isEditing) {
      return (
        <div className="flex items-center gap-1">
          <input
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            className="flex-1 px-2 py-1 text-xs border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            autoFocus
            onKeyDown={(e) => {
              if (e.key === 'Enter') saveEdit();
              if (e.key === 'Escape') cancelEdit();
            }}
          />
          <button
            onClick={saveEdit}
            className="p-1 text-green-600 hover:bg-green-50 rounded"
          >
            <Check className="h-3 w-3" />
          </button>
          <button
            onClick={cancelEdit}
            className="p-1 text-red-600 hover:bg-red-50 rounded"
          >
            <X className="h-3 w-3" />
          </button>
        </div>
      );
    }

    // 处理布尔值显示
    let displayValue = value;
    if (typeof value === 'boolean') {
      displayValue = value ? '是' : '否';
    }

    return (
      <div
        className={`group flex items-center justify-between cursor-pointer p-1 rounded ${
          hasError ? 'bg-red-50 text-red-800' : 'hover:bg-gray-50'
        }`}
        onClick={() => startEdit(rowIndex, field)}
      >
        <span className="text-xs truncate flex-1">
          {field === 'password' ? '••••••••' : String(displayValue || '')}
        </span>
        <Edit3 className="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 ml-1" />
      </div>
    );
  };

  if (data.length === 0) {
    return (
      <div className={`text-center py-8 text-gray-500 ${className}`}>
        <Users className="h-12 w-12 mx-auto mb-3 text-gray-300" />
        <p>暂无数据预览</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 统计信息 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">数据预览</h3>
        <div className="flex items-center gap-4 text-sm">
          <span className="text-gray-600">总计: {stats.total}</span>
          <span className="text-green-600">有效: {stats.valid}</span>
          {stats.errors > 0 && (
            <span className="text-red-600">错误: {stats.errors}</span>
          )}
        </div>
      </div>

      {/* 数据表格 */}
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="w-12 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                  #
                </th>
                {fields.map((field) => (
                  <th
                    key={field.key}
                    className={`${field.width} px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase`}
                  >
                    {field.label}
                    {field.required && <span className="text-red-500 ml-1">*</span>}
                  </th>
                ))}
                <th className="w-16 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.map((row, rowIndex) => {
                const hasRowError = validationErrors[rowIndex];
                return (
                  <tr
                    key={rowIndex}
                    className={hasRowError ? 'bg-red-50' : 'hover:bg-gray-50'}
                  >
                    <td className="px-3 py-2 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        {rowIndex + 1}
                        {hasRowError && (
                          <AlertTriangle className="h-3 w-3 text-red-500" />
                        )}
                      </div>
                    </td>
                    {fields.map((field) => (
                      <td key={field.key} className="px-3 py-2">
                        {renderCellContent(row, field.key, rowIndex)}
                        {validationErrors[rowIndex]?.[field.key] && (
                          <div className="text-xs text-red-600 mt-1">
                            {validationErrors[rowIndex][field.key]}
                          </div>
                        )}
                      </td>
                    ))}
                    <td className="px-3 py-2">
                      <button
                        onClick={() => deleteRow(rowIndex)}
                        className="p-1 text-red-600 hover:bg-red-50 rounded"
                        title="删除行"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* 操作提示 */}
      <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded">
        💡 提示：点击任意单元格可以编辑内容，按 Enter 保存，按 Esc 取消
      </div>
    </div>
  );
}
