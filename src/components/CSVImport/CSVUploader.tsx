'use client';

import { useCallback, useState } from 'react';
import { Upload, FileText, AlertCircle, Download } from 'lucide-react';

interface CSVUploaderProps {
  onFileSelect: (file: File) => void;
  isLoading?: boolean;
  className?: string;
}

export default function CSVUploader({ 
  onFileSelect, 
  isLoading = false, 
  className = '' 
}: CSVUploaderProps) {
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 验证文件类型和大小
  const validateFile = (file: File): string | null => {
    // 检查文件类型
    const validTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    const isValidType = validTypes.includes(file.type) || 
                       file.name.toLowerCase().endsWith('.csv') ||
                       file.name.toLowerCase().endsWith('.xlsx') ||
                       file.name.toLowerCase().endsWith('.xls');
    
    if (!isValidType) {
      return '请选择 CSV 或 Excel 文件';
    }

    // 检查文件大小 (最大 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      return '文件大小不能超过 10MB';
    }

    return null;
  };

  // 处理文件选择
  const handleFileSelect = useCallback((file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError(null);
    onFileSelect(file);
  }, [onFileSelect]);

  // 处理拖拽事件
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // 处理文件拖放
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  }, [handleFileSelect]);

  // 处理文件输入
  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  }, [handleFileSelect]);

  // 下载模板文件
  const downloadTemplate = () => {
    const csvContent = [
      'primaryEmail,givenName,familyName,password,orgUnitPath,suspended,changePasswordAtNextLogin,includeInGlobalAddressList',
      '<EMAIL>,三,张,SecurePass123,/,false,true,true',
      '<EMAIL>,四,李,StrongPass456,/工程部,false,true,true',
      '<EMAIL>,五,王,SafePass789,/销售部,false,true,true'
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'user_template.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 模板下载 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">CSV 批量导入</h3>
        <button
          onClick={downloadTemplate}
          className="flex items-center gap-2 px-3 py-2 text-sm text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <Download className="h-4 w-4" />
          下载模板
        </button>
      </div>

      {/* 文件上传区域 */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
          dragActive
            ? 'border-blue-400 bg-blue-50'
            : error
            ? 'border-red-300 bg-red-50'
            : 'border-gray-300 bg-gray-50 hover:bg-gray-100'
        } ${isLoading ? 'opacity-50 pointer-events-none' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          accept=".csv,.xlsx,.xls"
          onChange={handleFileInput}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          disabled={isLoading}
        />

        <div className="text-center">
          {isLoading ? (
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-3"></div>
              <p className="text-sm text-gray-600">正在处理文件...</p>
            </div>
          ) : (
            <>
              <Upload className={`mx-auto h-12 w-12 mb-3 ${
                dragActive ? 'text-blue-500' : 'text-gray-400'
              }`} />
              <p className="text-lg font-medium text-gray-900 mb-2">
                {dragActive ? '释放文件以上传' : '拖拽文件到此处或点击选择'}
              </p>
              <p className="text-sm text-gray-500 mb-4">
                支持 CSV、Excel (.xlsx, .xls) 格式，最大 10MB
              </p>
              <div className="flex items-center justify-center gap-4 text-xs text-gray-400">
                <div className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  <span>CSV</span>
                </div>
                <div className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  <span>Excel</span>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <span className="text-sm text-red-800">{error}</span>
        </div>
      )}

      {/* 使用说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">📋 使用说明</h4>
        <ul className="text-xs text-blue-800 space-y-1">
          <li>• 请先下载模板文件，按照格式填写用户信息</li>
          <li>• 必填字段：邮箱地址、名字、姓氏、密码</li>
          <li>• 可选字段：组织单位路径、账户状态等</li>
          <li>• 密码必须符合强度要求（至少8位，包含大小写字母和数字）</li>
          <li>• 邮箱地址必须是有效格式且在您的域名下</li>
        </ul>
      </div>
    </div>
  );
}
