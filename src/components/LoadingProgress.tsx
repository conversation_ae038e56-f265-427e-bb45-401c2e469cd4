'use client';

import { useState, useEffect } from 'react';
import { Loader2, Clock, Users, CheckCircle, XCircle } from 'lucide-react';

interface LoadingProgressProps {
  isLoading: boolean;
  totalUsers: number;
  currentStep?: string;
  progress?: number; // 0-100
  successCount?: number;
  errorCount?: number;
  estimatedTimeRemaining?: number; // 秒
  onCancel?: () => void;
  className?: string;
}

export default function LoadingProgress({
  isLoading,
  totalUsers,
  currentStep = '准备创建用户...',
  progress = 0,
  successCount = 0,
  errorCount = 0,
  estimatedTimeRemaining,
  onCancel,
  className = '',
}: LoadingProgressProps) {
  const [elapsedTime, setElapsedTime] = useState(0);

  // 计算已处理的用户数
  const processedUsers = successCount + errorCount;
  const remainingUsers = totalUsers - processedUsers;

  // 计算实际进度百分比
  const actualProgress = totalUsers > 0 ? (processedUsers / totalUsers) * 100 : progress;

  // 计算预估剩余时间
  useEffect(() => {
    if (!isLoading) {
      setElapsedTime(0);
      return;
    }

    const timer = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [isLoading]);

  // 格式化时间显示
  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}秒`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  };

  // 计算预估剩余时间
  const calculateEstimatedTime = (): number | null => {
    if (estimatedTimeRemaining !== undefined) {
      return estimatedTimeRemaining;
    }
    
    if (processedUsers > 0 && elapsedTime > 0) {
      const avgTimePerUser = elapsedTime / processedUsers;
      return Math.ceil(avgTimePerUser * remainingUsers);
    }
    
    return null;
  };

  const estimatedTime = calculateEstimatedTime();

  if (!isLoading) {
    return null;
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 shadow-sm ${className}`}>
      {/* 标题和取消按钮 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
          <h3 className="text-lg font-medium text-gray-900">正在创建用户</h3>
        </div>
        {onCancel && (
          <button
            onClick={onCancel}
            className="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            取消
          </button>
        )}
      </div>

      {/* 当前步骤 */}
      <div className="mb-4">
        <p className="text-sm text-gray-600">{currentStep}</p>
      </div>

      {/* 进度条 */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            进度: {processedUsers}/{totalUsers} 用户
          </span>
          <span className="text-sm text-gray-500">
            {Math.round(actualProgress)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${actualProgress}%` }}
          />
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-gray-500" />
          <div>
            <p className="text-xs text-gray-500">总计</p>
            <p className="text-sm font-medium">{totalUsers}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <div>
            <p className="text-xs text-gray-500">成功</p>
            <p className="text-sm font-medium text-green-600">{successCount}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <XCircle className="h-4 w-4 text-red-500" />
          <div>
            <p className="text-xs text-gray-500">失败</p>
            <p className="text-sm font-medium text-red-600">{errorCount}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4 text-blue-500" />
          <div>
            <p className="text-xs text-gray-500">已用时</p>
            <p className="text-sm font-medium">{formatTime(elapsedTime)}</p>
          </div>
        </div>
      </div>

      {/* 预估剩余时间 */}
      {estimatedTime !== null && estimatedTime > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-blue-600" />
            <span className="text-sm text-blue-800">
              预计剩余时间: {formatTime(estimatedTime)}
            </span>
          </div>
        </div>
      )}

      {/* 提示信息 */}
      <div className="mt-4 text-xs text-gray-500">
        <p>💡 提示: 请保持页面打开，创建过程中请勿关闭浏览器</p>
      </div>
    </div>
  );
}
