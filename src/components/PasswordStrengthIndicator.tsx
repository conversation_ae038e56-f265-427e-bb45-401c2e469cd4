'use client';

import { useMemo } from 'react';
import { Check, X, AlertCircle } from 'lucide-react';

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
}

interface PasswordCriteria {
  label: string;
  test: (password: string) => boolean;
  weight: number;
}

const passwordCriteria: PasswordCriteria[] = [
  {
    label: '至少8个字符',
    test: (password) => password.length >= 8,
    weight: 20,
  },
  {
    label: '包含小写字母',
    test: (password) => /[a-z]/.test(password),
    weight: 20,
  },
  {
    label: '包含大写字母',
    test: (password) => /[A-Z]/.test(password),
    weight: 20,
  },
  {
    label: '包含数字',
    test: (password) => /\d/.test(password),
    weight: 20,
  },
  {
    label: '包含特殊字符',
    test: (password) => /[!@#$%^&*(),.?":{}|<>]/.test(password),
    weight: 10,
  },
  {
    label: '至少12个字符',
    test: (password) => password.length >= 12,
    weight: 10,
  },
];

export default function PasswordStrengthIndicator({ 
  password, 
  className = '' 
}: PasswordStrengthIndicatorProps) {
  const analysis = useMemo(() => {
    if (!password) {
      return {
        score: 0,
        strength: 'none' as const,
        passedCriteria: [],
        failedCriteria: passwordCriteria,
      };
    }

    const passedCriteria = passwordCriteria.filter(criteria => criteria.test(password));
    const failedCriteria = passwordCriteria.filter(criteria => !criteria.test(password));
    const score = passedCriteria.reduce((sum, criteria) => sum + criteria.weight, 0);

    let strength: 'weak' | 'medium' | 'strong' | 'very-strong' | 'none';
    if (score < 40) {
      strength = 'weak';
    } else if (score < 60) {
      strength = 'medium';
    } else if (score < 80) {
      strength = 'strong';
    } else {
      strength = 'very-strong';
    }

    return {
      score,
      strength,
      passedCriteria,
      failedCriteria,
    };
  }, [password]);

  const strengthConfig = {
    none: { color: 'bg-gray-200', text: '请输入密码', textColor: 'text-gray-500' },
    weak: { color: 'bg-red-500', text: '弱', textColor: 'text-red-600' },
    medium: { color: 'bg-yellow-500', text: '中等', textColor: 'text-yellow-600' },
    strong: { color: 'bg-blue-500', text: '强', textColor: 'text-blue-600' },
    'very-strong': { color: 'bg-green-500', text: '很强', textColor: 'text-green-600' },
  };

  const config = strengthConfig[analysis.strength];

  if (!password) {
    return (
      <div className={`mt-2 ${className}`}>
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <AlertCircle className="h-4 w-4" />
          <span>密码强度将在您输入时显示</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`mt-2 space-y-2 ${className}`}>
      {/* 强度条 */}
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-600">密码强度:</span>
        <div className="flex-1 bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${config.color}`}
            style={{ width: `${analysis.score}%` }}
          />
        </div>
        <span className={`text-sm font-medium ${config.textColor}`}>
          {config.text}
        </span>
      </div>

      {/* 详细要求 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-xs">
        {passwordCriteria.slice(0, 4).map((criteria, index) => {
          const passed = analysis.passedCriteria.includes(criteria);
          return (
            <div
              key={index}
              className={`flex items-center gap-1 ${
                passed ? 'text-green-600' : 'text-gray-500'
              }`}
            >
              {passed ? (
                <Check className="h-3 w-3" />
              ) : (
                <X className="h-3 w-3" />
              )}
              <span>{criteria.label}</span>
            </div>
          );
        })}
      </div>

      {/* 额外建议 */}
      {analysis.strength === 'weak' && (
        <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
          💡 建议：使用更长的密码并包含大小写字母、数字和特殊字符
        </div>
      )}
      
      {analysis.strength === 'very-strong' && (
        <div className="text-xs text-green-600 bg-green-50 p-2 rounded">
          ✅ 优秀！这是一个非常安全的密码
        </div>
      )}
    </div>
  );
}
