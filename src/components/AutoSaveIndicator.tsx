'use client';

import { useState, useEffect } from 'react';
import { Save, Clock, AlertCircle, CheckCircle } from 'lucide-react';

interface AutoSaveIndicatorProps {
  hasSavedData: boolean;
  savedDataInfo?: {
    timestamp: number;
    timeAgo: string;
    userCount: number;
  } | null;
  onRestore?: () => void;
  onClearSaved?: () => void;
  className?: string;
}

export default function AutoSaveIndicator({
  hasSavedData,
  savedDataInfo,
  onRestore,
  onClearSaved,
  className = '',
}: AutoSaveIndicatorProps) {
  const [showSaveNotification, setShowSaveNotification] = useState(false);
  const [showRestorePrompt, setShowRestorePrompt] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // 确保只在客户端渲染时间相关内容
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 检查是否需要显示恢复提示
  useEffect(() => {
    if (hasSavedData && savedDataInfo) {
      setShowRestorePrompt(true);
    }
  }, [hasSavedData, savedDataInfo]);

  // 显示保存成功通知
  const showSaveSuccess = () => {
    setShowSaveNotification(true);
    setTimeout(() => setShowSaveNotification(false), 3000);
  };

  // 处理恢复数据
  const handleRestore = () => {
    onRestore?.();
    setShowRestorePrompt(false);
  };

  // 处理忽略保存的数据
  const handleIgnore = () => {
    setShowRestorePrompt(false);
  };

  // 处理清除保存的数据
  const handleClearSaved = () => {
    onClearSaved?.();
    setShowRestorePrompt(false);
  };

  return (
    <div className={className}>
      {/* 恢复数据提示 */}
      {showRestorePrompt && savedDataInfo && (
        <div className="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-blue-900 mb-1">
                发现未完成的表单数据
              </h4>
              <p className="text-sm text-blue-800 mb-3">
                我们发现您有一个自动保存的表单草稿，包含 {savedDataInfo.userCount} 个用户，
                保存于 {savedDataInfo.timeAgo}。是否要恢复这些数据？
              </p>
              <div className="flex gap-2">
                <button
                  onClick={handleRestore}
                  className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  恢复数据
                </button>
                <button
                  onClick={handleIgnore}
                  className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  忽略
                </button>
                <button
                  onClick={handleClearSaved}
                  className="px-3 py-1 text-red-600 text-sm hover:bg-red-50 rounded focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  删除草稿
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 自动保存成功通知 */}
      {showSaveNotification && (
        <div className="fixed top-4 right-4 bg-green-50 border border-green-200 rounded-lg p-3 shadow-lg z-50 animate-in slide-in-from-right">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="text-sm text-green-800">表单已自动保存</span>
          </div>
        </div>
      )}

      {/* 自动保存状态指示器 */}
      <div className="flex items-center gap-2 text-xs text-gray-500">
        <Save className="h-3 w-3" />
        <span>表单数据会自动保存</span>
        {isClient && savedDataInfo && (
          <>
            <span>•</span>
            <Clock className="h-3 w-3" />
            <span>上次保存: {savedDataInfo.timeAgo}</span>
          </>
        )}
      </div>
    </div>
  );
}

// 导出显示保存成功通知的函数，供外部调用
export const showAutoSaveSuccess = () => {
  // 创建临时通知元素
  const notification = document.createElement('div');
  notification.className = 'fixed top-4 right-4 bg-green-50 border border-green-200 rounded-lg p-3 shadow-lg z-50';
  notification.innerHTML = `
    <div class="flex items-center gap-2">
      <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      <span class="text-sm text-green-800">表单已自动保存</span>
    </div>
  `;

  document.body.appendChild(notification);

  // 3秒后移除通知
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
};
