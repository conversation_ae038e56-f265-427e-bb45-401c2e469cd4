'use client';

import { useState, useMemo } from 'react';
import { 
  User as UserIcon, 
  Mail, 
  Calendar, 
  Building, 
  MoreVertical,
  Edit,
  Trash2,
  UserChe<PERSON>,
  UserX,
  Eye
} from 'lucide-react';
import { useUserStore, User } from '@/store/userStore';

interface UserListProps {
  onUserSelect?: (user: User) => void;
  onUserEdit?: (user: User) => void;
  className?: string;
}

export default function UserList({ 
  onUserSelect, 
  onUserEdit, 
  className = '' 
}: UserListProps) {
  const {
    getPaginatedUsers,
    selectedUsers,
    toggleUserSelection,
    selectAllUsers,
    clearSelection,
    isLoading,
    currentPage,
    pageSize,
    setPage,
    getFilteredUsers,
  } = useUserStore();

  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const paginatedUsers = getPaginatedUsers();
  const filteredUsers = getFilteredUsers();
  const totalPages = Math.ceil(filteredUsers.length / pageSize);
  const isAllSelected = paginatedUsers.length > 0 && 
    paginatedUsers.every(user => selectedUsers.has(user.id));
  const isPartiallySelected = paginatedUsers.some(user => selectedUsers.has(user.id)) && 
    !isAllSelected;

  // 处理全选
  const handleSelectAll = () => {
    if (isAllSelected) {
      clearSelection();
    } else {
      selectAllUsers();
    }
  };

  // 格式化时间
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // 格式化最后登录时间
  const formatLastLogin = (dateString?: string) => {
    if (!dateString) return '从未登录';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return '今天';
    if (diffDays === 1) return '昨天';
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
    return formatDate(dateString);
  };

  // 用户操作菜单
  const UserActionMenu = ({ user }: { user: User }) => (
    <div className="absolute right-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
      <div className="py-1">
        <button
          onClick={() => {
            onUserSelect?.(user);
            setActiveDropdown(null);
          }}
          className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
        >
          <Eye className="h-4 w-4" />
          查看详情
        </button>
        <button
          onClick={() => {
            onUserEdit?.(user);
            setActiveDropdown(null);
          }}
          className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
        >
          <Edit className="h-4 w-4" />
          编辑用户
        </button>
        <div className="border-t border-gray-100 my-1" />
        <button
          onClick={() => {
            // 处理启用/暂停
            setActiveDropdown(null);
          }}
          className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
        >
          {user.suspended ? (
            <>
              <UserCheck className="h-4 w-4" />
              启用用户
            </>
          ) : (
            <>
              <UserX className="h-4 w-4" />
              暂停用户
            </>
          )}
        </button>
        <button
          onClick={() => {
            // 处理删除
            setActiveDropdown(null);
          }}
          className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
        >
          <Trash2 className="h-4 w-4" />
          删除用户
        </button>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="bg-white border border-gray-200 rounded-lg p-4 animate-pulse">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-300 rounded w-1/4"></div>
                <div className="h-3 bg-gray-300 rounded w-1/3"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 列表头部 */}
      <div className="bg-white border border-gray-200 rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={(input) => {
                    if (input) input.indeterminate = isPartiallySelected;
                  }}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">
                  {selectedUsers.size > 0 ? `已选择 ${selectedUsers.size} 个用户` : '全选'}
                </span>
              </label>
            </div>
            <div className="text-sm text-gray-500">
              显示 {(currentPage - 1) * pageSize + 1}-{Math.min(currentPage * pageSize, filteredUsers.length)} 
              / 共 {filteredUsers.length} 个用户
            </div>
          </div>
        </div>

        {/* 用户列表 */}
        <div className="divide-y divide-gray-200">
          {paginatedUsers.map((user) => (
            <div
              key={user.id}
              className={`px-6 py-4 hover:bg-gray-50 transition-colors ${
                selectedUsers.has(user.id) ? 'bg-blue-50' : ''
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* 选择框 */}
                  <input
                    type="checkbox"
                    checked={selectedUsers.has(user.id)}
                    onChange={() => toggleUserSelection(user.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />

                  {/* 用户头像 */}
                  <div className="flex-shrink-0">
                    {user.photoUrl ? (
                      <img
                        src={user.photoUrl}
                        alt={`${user.familyName}${user.givenName}`}
                        className="w-10 h-10 rounded-full"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <UserIcon className="w-5 h-5 text-gray-600" />
                      </div>
                    )}
                  </div>

                  {/* 用户信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {user.familyName}{user.givenName}
                      </p>
                      {user.suspended && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          已暂停
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <Mail className="w-3 h-3" />
                        <span className="truncate">{user.primaryEmail}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Building className="w-3 h-3" />
                        <span>{user.orgUnitPath}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>最后登录: {formatLastLogin(user.lastLoginTime)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center gap-2">
                  <div className="text-xs text-gray-500">
                    创建于 {formatDate(user.creationTime)}
                  </div>
                  <div className="relative">
                    <button
                      onClick={() => setActiveDropdown(
                        activeDropdown === user.id ? null : user.id
                      )}
                      className="p-1 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <MoreVertical className="w-4 h-4 text-gray-400" />
                    </button>
                    {activeDropdown === user.id && (
                      <UserActionMenu user={user} />
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                第 {currentPage} 页，共 {totalPages} 页
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  上一页
                </button>
                
                {/* 页码 */}
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }
                    
                    return (
                      <button
                        key={pageNum}
                        onClick={() => setPage(pageNum)}
                        className={`px-3 py-1 text-sm border rounded ${
                          pageNum === currentPage
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>
                
                <button
                  onClick={() => setPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下一页
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 空状态 */}
      {paginatedUsers.length === 0 && !isLoading && (
        <div className="bg-white border border-gray-200 rounded-lg p-12 text-center">
          <UserIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到用户</h3>
          <p className="text-gray-500">
            尝试调整搜索条件或筛选器来查找用户
          </p>
        </div>
      )}

      {/* 点击外部关闭下拉菜单 */}
      {activeDropdown && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setActiveDropdown(null)}
        />
      )}
    </div>
  );
}
