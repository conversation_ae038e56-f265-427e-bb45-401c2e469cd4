'use client';

import { useEffect, useState } from 'react';
import { Users, RefreshCw, Plus } from 'lucide-react';
import { useUserStore, User } from '@/store/userStore';
import UserSearch from './UserSearch';
import UserList from './UserList';
import BatchOperations from './BatchOperations';

interface UserManagementProps {
  onCreateUser?: () => void;
  className?: string;
}

export default function UserManagement({ 
  onCreateUser, 
  className = '' 
}: UserManagementProps) {
  const {
    fetchUsers,
    isLoading,
    error,
    selectedUsers,
    getFilteredUsers,
  } = useUserStore();

  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserDetail, setShowUserDetail] = useState(false);

  // 组件挂载时获取用户数据
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // 处理刷新
  const handleRefresh = async () => {
    await fetchUsers();
  };

  // 处理用户选择
  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
    setShowUserDetail(true);
  };

  // 处理用户编辑
  const handleUserEdit = (user: User) => {
    setSelectedUser(user);
    // 这里可以打开编辑模态框或跳转到编辑页面
    console.log('编辑用户:', user);
  };

  const filteredUsers = getFilteredUsers();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 页面头部 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Users className="h-6 w-6 text-blue-600" />
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">用户管理</h1>
                <p className="mt-1 text-sm text-gray-600">
                  管理您的 Google Workspace 用户账户
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={isLoading}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                刷新
              </button>
              {onCreateUser && (
                <button
                  onClick={onCreateUser}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <Plus className="h-4 w-4" />
                  添加用户
                </button>
              )}
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-blue-600">总用户数</p>
                  <p className="text-2xl font-semibold text-blue-900">
                    {filteredUsers.length}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-green-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">✓</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-green-600">活跃用户</p>
                  <p className="text-2xl font-semibold text-green-900">
                    {filteredUsers.filter(u => !u.suspended).length}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-yellow-50 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-yellow-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">⏸</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-yellow-600">已暂停</p>
                  <p className="text-2xl font-semibold text-yellow-900">
                    {filteredUsers.filter(u => u.suspended).length}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">✓</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-purple-600">已选择</p>
                  <p className="text-2xl font-semibold text-purple-900">
                    {selectedUsers.size}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <div className="h-5 w-5 bg-red-600 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">!</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-red-900">加载失败</h3>
              <p className="text-sm text-red-800 mt-1">{error}</p>
            </div>
            <button
              onClick={handleRefresh}
              className="ml-auto px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              重试
            </button>
          </div>
        </div>
      )}

      {/* 搜索和筛选 */}
      <UserSearch />

      {/* 批量操作 */}
      {selectedUsers.size > 0 && (
        <BatchOperations />
      )}

      {/* 用户列表 */}
      <UserList
        onUserSelect={handleUserSelect}
        onUserEdit={handleUserEdit}
      />

      {/* 用户详情模态框 */}
      {showUserDetail && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">用户详情</h2>
              <button
                onClick={() => setShowUserDetail(false)}
                className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <span className="sr-only">关闭</span>
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="space-y-6">
              {/* 基本信息 */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">姓名</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedUser.familyName}{selectedUser.givenName}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">邮箱地址</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.primaryEmail}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">组织单位</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.orgUnitPath}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">状态</label>
                    <p className="mt-1">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        selectedUser.suspended 
                          ? 'bg-red-100 text-red-800' 
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {selectedUser.suspended ? '已暂停' : '活跃'}
                      </span>
                    </p>
                  </div>
                </div>
              </div>

              {/* 账户信息 */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">账户信息</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">创建时间</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(selectedUser.creationTime).toLocaleString('zh-CN')}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">最后登录</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedUser.lastLoginTime 
                        ? new Date(selectedUser.lastLoginTime).toLocaleString('zh-CN')
                        : '从未登录'
                      }
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">下次登录时更改密码</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedUser.changePasswordAtNextLogin ? '是' : '否'}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">包含在全局地址列表</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedUser.includeInGlobalAddressList ? '是' : '否'}
                    </p>
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-end gap-3 pt-6 border-t border-gray-200">
                <button
                  onClick={() => setShowUserDetail(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  关闭
                </button>
                <button
                  onClick={() => handleUserEdit(selectedUser)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  编辑用户
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
