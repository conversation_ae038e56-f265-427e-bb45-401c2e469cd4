'use client';

import { useState } from 'react';
import { 
  User<PERSON>he<PERSON>, 
  UserX, 
  Trash2, 
  Building, 
  Download,
  AlertTriangle,
  X
} from 'lucide-react';
import { useUserStore, BatchOperation } from '@/store/userStore';

interface BatchOperationsProps {
  className?: string;
}

export default function BatchOperations({ className = '' }: BatchOperationsProps) {
  const {
    selectedUsers,
    getSelectedUsersData,
    batchOperation,
    clearSelection,
    isUpdating,
  } = useUserStore();

  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingOperation, setPendingOperation] = useState<{
    type: BatchOperation;
    data?: any;
  } | null>(null);
  const [orgUnitPath, setOrgUnitPath] = useState('/');

  const selectedUsersData = getSelectedUsersData();
  const selectedCount = selectedUsers.size;

  // 组织单位选项
  const orgUnitOptions = [
    { value: '/', label: '根组织单位' },
    { value: '/工程部', label: '工程部' },
    { value: '/销售部', label: '销售部' },
    { value: '/人事部', label: '人事部' },
    { value: '/财务部', label: '财务部' },
  ];

  // 执行批量操作
  const handleBatchOperation = async (operation: BatchOperation, data?: any) => {
    if (selectedCount === 0) return;

    setPendingOperation({ type: operation, data });
    setShowConfirmDialog(true);
  };

  // 确认执行操作
  const confirmOperation = async () => {
    if (!pendingOperation) return;

    try {
      await batchOperation(
        pendingOperation.type,
        Array.from(selectedUsers),
        pendingOperation.data
      );
      setShowConfirmDialog(false);
      setPendingOperation(null);
    } catch (error) {
      console.error('批量操作失败:', error);
    }
  };

  // 取消操作
  const cancelOperation = () => {
    setShowConfirmDialog(false);
    setPendingOperation(null);
  };

  // 导出用户数据
  const exportUsers = () => {
    const csvContent = [
      'primaryEmail,givenName,familyName,orgUnitPath,suspended,creationTime,lastLoginTime',
      ...selectedUsersData.map(user => [
        user.primaryEmail,
        user.givenName,
        user.familyName,
        user.orgUnitPath,
        user.suspended,
        user.creationTime,
        user.lastLoginTime || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `users_export_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 获取操作描述
  const getOperationDescription = () => {
    if (!pendingOperation) return '';

    const count = selectedCount;
    switch (pendingOperation.type) {
      case 'activate':
        return `确定要启用选中的 ${count} 个用户吗？`;
      case 'suspend':
        return `确定要暂停选中的 ${count} 个用户吗？暂停后用户将无法登录。`;
      case 'delete':
        return `确定要删除选中的 ${count} 个用户吗？此操作不可撤销！`;
      case 'changeOrgUnit':
        return `确定要将选中的 ${count} 个用户移动到 "${pendingOperation.data?.orgUnitPath}" 吗？`;
      default:
        return '';
    }
  };

  // 获取操作按钮样式
  const getOperationButtonClass = (type: BatchOperation) => {
    const baseClass = "flex items-center gap-2 px-3 py-2 text-sm rounded-md focus:outline-none focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed";
    
    switch (type) {
      case 'activate':
        return `${baseClass} bg-green-600 text-white hover:bg-green-700 focus:ring-green-500`;
      case 'suspend':
        return `${baseClass} bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500`;
      case 'delete':
        return `${baseClass} bg-red-600 text-white hover:bg-red-700 focus:ring-red-500`;
      case 'changeOrgUnit':
        return `${baseClass} bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500`;
      default:
        return `${baseClass} bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500`;
    }
  };

  if (selectedCount === 0) {
    return null;
  }

  return (
    <>
      <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium text-gray-900">
              已选择 {selectedCount} 个用户
            </span>
            <button
              onClick={clearSelection}
              className="text-sm text-gray-500 hover:text-gray-700 underline"
            >
              清除选择
            </button>
          </div>

          <div className="flex items-center gap-2">
            {/* 启用用户 */}
            <button
              onClick={() => handleBatchOperation('activate')}
              disabled={isUpdating}
              className={getOperationButtonClass('activate')}
            >
              <UserCheck className="h-4 w-4" />
              启用
            </button>

            {/* 暂停用户 */}
            <button
              onClick={() => handleBatchOperation('suspend')}
              disabled={isUpdating}
              className={getOperationButtonClass('suspend')}
            >
              <UserX className="h-4 w-4" />
              暂停
            </button>

            {/* 更改组织单位 */}
            <div className="flex items-center gap-1">
              <select
                value={orgUnitPath}
                onChange={(e) => setOrgUnitPath(e.target.value)}
                className="px-2 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {orgUnitOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <button
                onClick={() => handleBatchOperation('changeOrgUnit', { orgUnitPath })}
                disabled={isUpdating}
                className={getOperationButtonClass('changeOrgUnit')}
              >
                <Building className="h-4 w-4" />
                移动
              </button>
            </div>

            {/* 导出数据 */}
            <button
              onClick={exportUsers}
              className="flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <Download className="h-4 w-4" />
              导出
            </button>

            {/* 删除用户 */}
            <button
              onClick={() => handleBatchOperation('delete')}
              disabled={isUpdating}
              className={getOperationButtonClass('delete')}
            >
              <Trash2 className="h-4 w-4" />
              删除
            </button>
          </div>
        </div>

        {/* 选中用户预览 */}
        {selectedCount <= 5 && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="text-xs text-gray-500 mb-2">选中的用户:</div>
            <div className="flex flex-wrap gap-2">
              {selectedUsersData.map((user) => (
                <span
                  key={user.id}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                >
                  {user.familyName}{user.givenName}
                  <span className="text-blue-600">({user.primaryEmail})</span>
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 确认对话框 */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  确认批量操作
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  {getOperationDescription()}
                </p>
                
                {pendingOperation?.type === 'delete' && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                    <p className="text-sm text-red-800">
                      ⚠️ 警告：删除操作不可撤销，请确保您真的要删除这些用户。
                    </p>
                  </div>
                )}

                <div className="flex items-center justify-end gap-3">
                  <button
                    onClick={cancelOperation}
                    className="px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    取消
                  </button>
                  <button
                    onClick={confirmOperation}
                    disabled={isUpdating}
                    className={`px-4 py-2 text-sm rounded-md focus:outline-none focus:ring-2 disabled:opacity-50 ${
                      pendingOperation?.type === 'delete'
                        ? 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
                        : 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
                    }`}
                  >
                    {isUpdating ? '处理中...' : '确认'}
                  </button>
                </div>
              </div>
              <button
                onClick={cancelOperation}
                className="flex-shrink-0 p-1 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <X className="h-4 w-4 text-gray-400" />
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
