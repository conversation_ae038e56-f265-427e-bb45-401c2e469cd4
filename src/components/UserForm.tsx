'use client';

import { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus, Trash2, Users, AlertCircle } from 'lucide-react';
import { bulkUserFormSchema, type BulkUserFormData, type UserFormData } from '@/lib/validations';
import { UserCreationResult } from '@/types/user';
import PasswordStrengthIndicator from './PasswordStrengthIndicator';
import LoadingProgress from './LoadingProgress';
import AutoSaveIndicator from './AutoSaveIndicator';
import { useAutoSave } from '@/hooks/useAutoSave';

interface UserFormProps {
  onSubmit: (data: BulkUserFormData) => Promise<UserCreationResult[]>;
  isLoading: boolean;
}

export default function UserForm({ onSubmit, isLoading }: UserFormProps) {
  const [results, setResults] = useState<UserCreationResult[]>([]);
  const [currentPasswords, setCurrentPasswords] = useState<string[]>(['']);

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
    reset,
    getValues,

  } = useForm({
    resolver: zodResolver(bulkUserFormSchema),
    defaultValues: {
      users: [
        {
          primaryEmail: '',
          givenName: '',
          familyName: '',
          password: '',
          orgUnitPath: '/',
          suspended: false,
          changePasswordAtNextLogin: true,
          includeInGlobalAddressList: true,
        },
      ],
    },
  });

  // 自动保存功能
  const {
    hasSavedData,
    getSavedDataInfo,
    restoreData,
    clearSavedData,
  } = useAutoSave({
    key: 'userForm',
    getValues,
    enabled: true,
    interval: 30000, // 30秒自动保存
    onSave: (data) => {
      console.log('表单已自动保存，包含', data.users.length, '个用户');
    },
    onRestore: (data) => {
      // 恢复表单数据
      reset(data);
      setCurrentPasswords(data.users.map(user => user.password));
      console.log('已恢复表单数据');
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'users',
  });

  const handleFormSubmit = async (data: BulkUserFormData) => {
    try {
      const results = await onSubmit(data);
      setResults(results);
    } catch (error) {
      console.error('提交表单失败:', error);
    }
  };

  const addUser = () => {
    const newUser: UserFormData = {
      primaryEmail: '',
      givenName: '',
      familyName: '',
      password: '',
      orgUnitPath: '/',
      suspended: false,
      changePasswordAtNextLogin: true,
      includeInGlobalAddressList: true,
    };
    append(newUser);
    setCurrentPasswords(prev => [...prev, '']);
  };

  const removeUser = (index: number) => {
    if (fields.length > 1) {
      remove(index);
      setCurrentPasswords(prev => prev.filter((_, i) => i !== index));
    }
  };

  // 处理密码变化
  const handlePasswordChange = (index: number, password: string) => {
    setCurrentPasswords(prev => {
      const newPasswords = [...prev];
      newPasswords[index] = password;
      return newPasswords;
    });
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* 自动保存指示器 */}
      <AutoSaveIndicator
        hasSavedData={hasSavedData()}
        savedDataInfo={getSavedDataInfo()}
        onRestore={restoreData}
        onClearSaved={clearSavedData}
        className="mb-4"
      />

      {/* 加载进度指示器 */}
      {isLoading && (
        <LoadingProgress
          isLoading={isLoading}
          totalUsers={fields.length}
          currentStep="正在创建用户账户..."
          className="mb-4"
        />
      )}

      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200 px-6 py-4">
          <div className="flex items-center gap-3">
            <Users className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-semibold text-gray-900">批量添加用户</h1>
          </div>
          <p className="mt-2 text-sm text-gray-600">
            添加新用户到您的 Google Workspace 域。请填写每个用户的必要信息。
          </p>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6">
          <div className="space-y-6">
            {fields.map((field, index) => (
              <div
                key={field.id}
                className="border border-gray-200 rounded-lg p-4 bg-gray-50"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    用户 {index + 1}
                  </h3>
                  {fields.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeUser(index)}
                      className="text-red-600 hover:text-red-800 p-1"
                      title="删除用户"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      邮箱地址 *
                    </label>
                    <input
                      {...register(`users.${index}.primaryEmail`)}
                      type="email"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                    {errors.users?.[index]?.primaryEmail && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.users[index]?.primaryEmail?.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      密码 *
                    </label>
                    <input
                      {...register(`users.${index}.password`, {
                        onChange: (e) => handlePasswordChange(index, e.target.value)
                      })}
                      type="password"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="至少8个字符"
                    />
                    {errors.users?.[index]?.password && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.users[index]?.password?.message}
                      </p>
                    )}
                    <PasswordStrengthIndicator
                      password={currentPasswords[index] || ''}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      名字 *
                    </label>
                    <input
                      {...register(`users.${index}.givenName`)}
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="张"
                    />
                    {errors.users?.[index]?.givenName && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.users[index]?.givenName?.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      姓氏 *
                    </label>
                    <input
                      {...register(`users.${index}.familyName`)}
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="三"
                    />
                    {errors.users?.[index]?.familyName && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.users[index]?.familyName?.message}
                      </p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      组织单位路径
                    </label>
                    <select
                      {...register(`users.${index}.orgUnitPath`)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="/">根组织单位</option>
                      <option value="/工程部">工程部</option>
                      <option value="/销售部">销售部</option>
                      <option value="/人事部">人事部</option>
                    </select>
                  </div>

                  <div className="md:col-span-2 space-y-2">
                    <label className="flex items-center">
                      <input
                        {...register(`users.${index}.changePasswordAtNextLogin`)}
                        type="checkbox"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        要求用户在下次登录时更改密码
                      </span>
                    </label>

                    <label className="flex items-center">
                      <input
                        {...register(`users.${index}.includeInGlobalAddressList`)}
                        type="checkbox"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        包含在全局地址列表中
                      </span>
                    </label>

                    <label className="flex items-center">
                      <input
                        {...register(`users.${index}.suspended`)}
                        type="checkbox"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        暂停用户账户
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 flex items-center justify-between">
            <button
              type="button"
              onClick={addUser}
              className="flex items-center gap-2 px-4 py-2 text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4" />
              添加用户
            </button>

            <div className="flex gap-3">
              <button
                type="button"
                onClick={() => {
                  reset();
                  setResults([]);
                  setCurrentPasswords(['']);
                  clearSavedData();
                }}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                重置
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? '创建中...' : '创建用户'}
              </button>
            </div>
          </div>
        </form>

        {/* 结果显示 */}
        {results.length > 0 && (
          <div className="border-t border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">创建结果</h3>
            <div className="space-y-2">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`flex items-center gap-3 p-3 rounded-md ${result.success
                    ? 'bg-green-50 text-green-800'
                    : 'bg-red-50 text-red-800'
                    }`}
                >
                  <AlertCircle className="h-4 w-4" />
                  <span className="font-medium">{result.email}</span>
                  <span>
                    {result.success ? '创建成功' : `创建失败: ${result.error}`}
                  </span>
                </div>
              ))}
            </div>
            <div className="mt-4 text-sm text-gray-600">
              总计: {results.length} 个用户，
              成功: {results.filter(r => r.success).length} 个，
              失败: {results.filter(r => !r.success).length} 个
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
