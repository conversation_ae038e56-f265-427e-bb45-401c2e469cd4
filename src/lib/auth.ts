import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';

// Google Admin SDK 所需的权限范围
const GOOGLE_SCOPES = [
  'https://www.googleapis.com/auth/admin.directory.user',
  'https://www.googleapis.com/auth/admin.directory.orgunit',
  'https://www.googleapis.com/auth/admin.directory.group',
  'https://www.googleapis.com/auth/userinfo.email',
  'https://www.googleapis.com/auth/userinfo.profile',
].join(' ');

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          scope: GOOGLE_SCOPES,
          access_type: 'offline',
          prompt: 'consent',
          hd: process.env.GOOGLE_WORKSPACE_DOMAIN, // 限制为 jhun.edu.kg 域
        },
      },
    }),
  ],
  callbacks: {
    async jwt({ token, account, user }) {
      // 保存访问令牌和刷新令牌
      if (account) {
        token.accessToken = account.access_token;
        token.refreshToken = account.refresh_token;
        token.expiresAt = account.expires_at;
      }
      
      // 检查令牌是否过期
      if (token.expiresAt && Date.now() < (token.expiresAt as number) * 1000) {
        return token;
      }
      
      // 刷新令牌
      return await refreshAccessToken(token);
    },
    async session({ session, token }) {
      // 将访问令牌传递给客户端
      session.accessToken = token.accessToken as string;
      session.error = token.error as string;
      
      // 验证用户是否为管理员
      if (session.user?.email) {
        session.isAdmin = await isUserAdmin(session.user.email);
      }
      
      return session;
    },
    async signIn({ user, account, profile }) {
      // 验证用户域名
      if (user.email && !user.email.endsWith('@jhun.edu.kg')) {
        console.log('用户不属于 jhun.edu.kg 域:', user.email);
        return false;
      }
      
      // 验证用户是否有管理员权限
      if (user.email) {
        const isAdmin = await isUserAdmin(user.email);
        if (!isAdmin) {
          console.log('用户没有管理员权限:', user.email);
          return false;
        }
      }
      
      return true;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

// 刷新访问令牌
async function refreshAccessToken(token: any) {
  try {
    const url = 'https://oauth2.googleapis.com/token';
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      method: 'POST',
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID!,
        client_secret: process.env.GOOGLE_CLIENT_SECRET!,
        grant_type: 'refresh_token',
        refresh_token: token.refreshToken,
      }),
    });

    const refreshedTokens = await response.json();

    if (!response.ok) {
      throw refreshedTokens;
    }

    return {
      ...token,
      accessToken: refreshedTokens.access_token,
      expiresAt: Date.now() / 1000 + refreshedTokens.expires_in,
      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken,
    };
  } catch (error) {
    console.error('刷新令牌失败:', error);
    return {
      ...token,
      error: 'RefreshAccessTokenError',
    };
  }
}

// 验证用户是否为管理员
async function isUserAdmin(email: string): Promise<boolean> {
  try {
    // 这里可以实现更复杂的管理员验证逻辑
    // 目前简单检查是否为指定的管理员邮箱
    const adminEmails = [
      '<EMAIL>',
      // 可以添加更多管理员邮箱
    ];
    
    return adminEmails.includes(email) || email.endsWith('@jhun.edu.kg');
  } catch (error) {
    console.error('验证管理员权限失败:', error);
    return false;
  }
}

// 扩展 NextAuth 类型
declare module 'next-auth' {
  interface Session {
    accessToken?: string;
    error?: string;
    isAdmin?: boolean;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: number;
    error?: string;
  }
}
