import { UserFormData } from '@/lib/validations';

interface ParseResult {
  data: UserFormData[];
  errors: string[];
  warnings: string[];
}

// 简单的 CSV 解析器
export function parseCSV(csvText: string): ParseResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const data: UserFormData[] = [];

  try {
    const lines = csvText.split('\n').filter(line => line.trim());
    
    if (lines.length === 0) {
      errors.push('文件为空');
      return { data, errors, warnings };
    }

    // 解析表头
    const headers = parseCSVLine(lines[0]);
    const expectedHeaders = [
      'primaryEmail',
      'givenName', 
      'familyName',
      'password',
      'orgUnitPath',
      'suspended',
      'changePasswordAtNextLogin',
      'includeInGlobalAddressList'
    ];

    // 检查必需的表头
    const requiredHeaders = ['primaryEmail', 'givenName', 'familyName', 'password'];
    const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
    
    if (missingHeaders.length > 0) {
      errors.push(`缺少必需的列: ${missingHeaders.join(', ')}`);
      return { data, errors, warnings };
    }

    // 检查未知的表头
    const unknownHeaders = headers.filter(header => !expectedHeaders.includes(header));
    if (unknownHeaders.length > 0) {
      warnings.push(`未知的列将被忽略: ${unknownHeaders.join(', ')}`);
    }

    // 解析数据行
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      try {
        const values = parseCSVLine(line);
        
        if (values.length !== headers.length) {
          warnings.push(`第 ${i + 1} 行列数不匹配，已跳过`);
          continue;
        }

        const rowData: Partial<UserFormData> = {};
        
        headers.forEach((header, index) => {
          const value = values[index]?.trim();
          
          switch (header) {
            case 'primaryEmail':
            case 'givenName':
            case 'familyName':
            case 'password':
            case 'orgUnitPath':
              rowData[header] = value || '';
              break;
            case 'suspended':
            case 'changePasswordAtNextLogin':
            case 'includeInGlobalAddressList':
              rowData[header] = parseBooleanValue(value);
              break;
          }
        });

        // 设置默认值
        const userData: UserFormData = {
          primaryEmail: rowData.primaryEmail || '',
          givenName: rowData.givenName || '',
          familyName: rowData.familyName || '',
          password: rowData.password || '',
          orgUnitPath: rowData.orgUnitPath || '/',
          suspended: rowData.suspended ?? false,
          changePasswordAtNextLogin: rowData.changePasswordAtNextLogin ?? true,
          includeInGlobalAddressList: rowData.includeInGlobalAddressList ?? true,
        };

        data.push(userData);
      } catch (error) {
        warnings.push(`第 ${i + 1} 行解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    if (data.length === 0) {
      errors.push('没有有效的数据行');
    }

  } catch (error) {
    errors.push(`文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }

  return { data, errors, warnings };
}

// 解析 CSV 行（处理引号和逗号）
function parseCSVLine(line: string): string[] {
  const result: string[] = [];
  let current = '';
  let inQuotes = false;
  let i = 0;

  while (i < line.length) {
    const char = line[i];
    const nextChar = line[i + 1];

    if (char === '"') {
      if (inQuotes && nextChar === '"') {
        // 转义的引号
        current += '"';
        i += 2;
      } else {
        // 开始或结束引号
        inQuotes = !inQuotes;
        i++;
      }
    } else if (char === ',' && !inQuotes) {
      // 字段分隔符
      result.push(current);
      current = '';
      i++;
    } else {
      current += char;
      i++;
    }
  }

  // 添加最后一个字段
  result.push(current);

  return result;
}

// 解析布尔值
function parseBooleanValue(value: string): boolean {
  if (!value) return false;
  
  const lowerValue = value.toLowerCase().trim();
  return lowerValue === 'true' || 
         lowerValue === '1' || 
         lowerValue === 'yes' || 
         lowerValue === 'y' ||
         lowerValue === '是';
}

// 生成 CSV 内容
export function generateCSV(data: UserFormData[]): string {
  const headers = [
    'primaryEmail',
    'givenName',
    'familyName', 
    'password',
    'orgUnitPath',
    'suspended',
    'changePasswordAtNextLogin',
    'includeInGlobalAddressList'
  ];

  const csvLines = [headers.join(',')];

  data.forEach(row => {
    const values = headers.map(header => {
      const value = row[header as keyof UserFormData];
      
      // 处理不同类型的值
      if (typeof value === 'boolean') {
        return value.toString();
      }
      
      if (typeof value === 'string') {
        // 如果包含逗号或引号，需要用引号包围
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }
      
      return String(value || '');
    });
    
    csvLines.push(values.join(','));
  });

  return csvLines.join('\n');
}

// 读取文件内容
export function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const result = event.target?.result;
      if (typeof result === 'string') {
        resolve(result);
      } else {
        reject(new Error('文件读取失败'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('文件读取错误'));
    };
    
    // 尝试以 UTF-8 编码读取
    reader.readAsText(file, 'UTF-8');
  });
}
