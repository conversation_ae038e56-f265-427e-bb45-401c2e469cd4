import { google } from 'googleapis';
import { UserFormData } from './validations';

export interface UserCreationResult {
  success: boolean;
  error?: string;
  email: string;
  userId?: string;
}

export interface GoogleUser {
  id?: string;
  primaryEmail: string;
  name: {
    givenName: string;
    familyName: string;
    fullName?: string;
  };
  password?: string;
  orgUnitPath: string;
  suspended: boolean;
  changePasswordAtNextLogin: boolean;
  includeInGlobalAddressList: boolean;
  recoveryEmail?: string;
  creationTime?: string;
  lastLoginTime?: string;
}

// 创建 Google Admin SDK 客户端（使用 OAuth 2.0 访问令牌）
function createAdminClient(accessToken: string) {
  const auth = new google.auth.OAuth2();
  auth.setCredentials({ access_token: accessToken });
  
  return google.admin({ version: 'directory_v1', auth });
}

// 创建单个用户
export async function createUser(
  userData: UserFormData, 
  accessToken: string
): Promise<UserCreationResult> {
  try {
    const admin = createAdminClient(accessToken);
    
    // 构建用户数据
    const userResource: any = {
      primaryEmail: userData.primaryEmail,
      name: {
        givenName: userData.givenName,
        familyName: userData.familyName,
        fullName: `${userData.familyName}${userData.givenName}`,
      },
      password: userData.password,
      orgUnitPath: userData.orgUnitPath || '/',
      suspended: userData.suspended || false,
      changePasswordAtNextLogin: userData.changePasswordAtNextLogin !== false,
      includeInGlobalAddressList: userData.includeInGlobalAddressList !== false,
    };

    // 如果有辅助邮箱，添加到用户资源中
    if (userData.recoveryEmail) {
      userResource.recoveryEmail = userData.recoveryEmail;
    }

    console.log('创建用户请求:', {
      email: userData.primaryEmail,
      domain: process.env.GOOGLE_WORKSPACE_DOMAIN,
    });

    // 调用 Google Admin SDK API 创建用户
    const response = await admin.users.insert({
      requestBody: userResource,
    });

    if (response.data && response.data.id) {
      console.log('用户创建成功:', response.data.primaryEmail);
      return {
        success: true,
        email: userData.primaryEmail,
        userId: response.data.id,
      };
    } else {
      return {
        success: false,
        error: '创建用户失败：API 返回无效响应',
        email: userData.primaryEmail,
      };
    }
  } catch (error: any) {
    console.error('创建用户失败:', error);
    
    let errorMessage = '创建用户时发生未知错误';
    
    if (error.response?.data?.error) {
      const apiError = error.response.data.error;
      
      // 处理常见的 API 错误
      switch (apiError.code) {
        case 409:
          errorMessage = '邮箱地址已存在';
          break;
        case 400:
          if (apiError.message?.includes('password')) {
            errorMessage = '密码不符合域策略要求';
          } else if (apiError.message?.includes('email')) {
            errorMessage = '邮箱地址格式无效';
          } else {
            errorMessage = `请求参数错误: ${apiError.message}`;
          }
          break;
        case 403:
          errorMessage = '权限不足，请确认您有创建用户的权限';
          break;
        case 429:
          errorMessage = 'API 调用频率超限，请稍后重试';
          break;
        case 500:
          errorMessage = 'Google 服务器内部错误，请稍后重试';
          break;
        default:
          errorMessage = `API 错误 (${apiError.code}): ${apiError.message}`;
      }
    } else if (error.message) {
      errorMessage = error.message;
    }

    return {
      success: false,
      error: errorMessage,
      email: userData.primaryEmail,
    };
  }
}

// 批量创建用户
export async function createUsers(
  users: UserFormData[], 
  accessToken: string,
  onProgress?: (completed: number, total: number, current: UserFormData) => void
): Promise<UserCreationResult[]> {
  const results: UserCreationResult[] = [];
  
  console.log(`开始批量创建 ${users.length} 个用户`);
  
  for (let i = 0; i < users.length; i++) {
    const user = users[i];
    
    try {
      // 调用进度回调
      if (onProgress) {
        onProgress(i, users.length, user);
      }
      
      const result = await createUser(user, accessToken);
      results.push(result);
      
      console.log(`用户 ${i + 1}/${users.length} 处理完成:`, result.success ? '成功' : '失败');
      
      // 添加延迟以避免 API 速率限制
      if (i < users.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    } catch (error: unknown) {
      console.error('创建用户失败:', error);
      results.push({
        success: false,
        error: error instanceof Error ? error.message : '创建用户时发生未知错误',
        email: user.primaryEmail,
      });
    }
  }
  
  // 调用最终进度回调
  if (onProgress) {
    onProgress(users.length, users.length, users[users.length - 1]);
  }
  
  console.log('批量创建完成，成功:', results.filter(r => r.success).length, '失败:', results.filter(r => !r.success).length);
  
  return results;
}

// 获取用户列表
export async function getUsers(
  accessToken: string,
  options: {
    domain?: string;
    maxResults?: number;
    pageToken?: string;
    query?: string;
    orderBy?: string;
  } = {}
): Promise<{
  users: GoogleUser[];
  nextPageToken?: string;
}> {
  try {
    const admin = createAdminClient(accessToken);
    
    console.log('获取用户列表，参数:', options);
    
    const response = await admin.users.list({
      domain: options.domain || process.env.GOOGLE_WORKSPACE_DOMAIN || 'jhun.edu.kg',
      maxResults: options.maxResults || 100,
      pageToken: options.pageToken,
      query: options.query,
      orderBy: options.orderBy,
    });

    const users: GoogleUser[] = (response.data.users || []).map(user => ({
      id: user.id,
      primaryEmail: user.primaryEmail!,
      name: {
        givenName: user.name?.givenName || '',
        familyName: user.name?.familyName || '',
        fullName: user.name?.fullName,
      },
      orgUnitPath: user.orgUnitPath || '/',
      suspended: user.suspended || false,
      changePasswordAtNextLogin: user.changePasswordAtNextLogin || false,
      includeInGlobalAddressList: user.includeInGlobalAddressList !== false,
      recoveryEmail: user.recoveryEmail,
      creationTime: user.creationTime,
      lastLoginTime: user.lastLoginTime,
    }));

    console.log(`获取到 ${users.length} 个用户`);

    return {
      users,
      nextPageToken: response.data.nextPageToken,
    };
  } catch (error: any) {
    console.error('获取用户列表失败:', error);
    throw new Error(`获取用户列表失败: ${error.message}`);
  }
}

// 更新用户
export async function updateUser(
  userId: string,
  updates: Partial<GoogleUser>,
  accessToken: string
): Promise<UserCreationResult> {
  try {
    const admin = createAdminClient(accessToken);
    
    console.log('更新用户:', userId, updates);
    
    const response = await admin.users.update({
      userKey: userId,
      requestBody: updates,
    });

    if (response.data) {
      return {
        success: true,
        email: response.data.primaryEmail!,
        userId: response.data.id,
      };
    } else {
      return {
        success: false,
        error: '更新用户失败：API 返回无效响应',
        email: updates.primaryEmail || userId,
      };
    }
  } catch (error: any) {
    console.error('更新用户失败:', error);
    return {
      success: false,
      error: error.message || '更新用户时发生未知错误',
      email: updates.primaryEmail || userId,
    };
  }
}

// 删除用户
export async function deleteUser(
  userId: string,
  accessToken: string
): Promise<UserCreationResult> {
  try {
    const admin = createAdminClient(accessToken);
    
    console.log('删除用户:', userId);
    
    await admin.users.delete({
      userKey: userId,
    });

    return {
      success: true,
      email: userId,
      userId,
    };
  } catch (error: any) {
    console.error('删除用户失败:', error);
    return {
      success: false,
      error: error.message || '删除用户时发生未知错误',
      email: userId,
    };
  }
}

// 获取组织单位列表
export async function getOrgUnits(accessToken: string) {
  try {
    const admin = createAdminClient(accessToken);
    
    const response = await admin.orgunits.list({
      customerId: 'my_customer',
    });
    
    return response.data.organizationUnits || [];
  } catch (error: any) {
    console.error('获取组织单位失败:', error);
    return [];
  }
}
