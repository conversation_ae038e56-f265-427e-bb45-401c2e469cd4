import { z } from 'zod';

export const userFormSchema = z.object({
  primaryEmail: z
    .string()
    .min(1, '邮箱地址是必填项')
    .email('请输入有效的邮箱地址')
    .refine((email) => email.endsWith('@jhun.edu.kg'), {
      message: '邮箱地址必须是 jhun.edu.kg 域名',
    }),
  givenName: z
    .string()
    .min(1, '名字是必填项')
    .max(60, '名字不能超过60个字符'),
  familyName: z
    .string()
    .min(1, '姓氏是必填项')
    .max(60, '姓氏不能超过60个字符'),
  password: z
    .string()
    .min(8, '密码至少需要8个字符')
    .max(100, '密码不能超过100个字符')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      '密码必须包含至少一个小写字母、一个大写字母和一个数字'
    ),
  recoveryEmail: z
    .string()
    .email('请输入有效的辅助邮箱地址')
    .optional()
    .or(z.literal('')),
  orgUnitPath: z.string().optional(),
  suspended: z.boolean().default(false),
  changePasswordAtNextLogin: z.boolean().default(true),
  includeInGlobalAddressList: z.boolean().default(true),
});

export const bulkUserFormSchema = z.object({
  users: z.array(userFormSchema).min(1, '至少需要添加一个用户'),
});

export type UserFormData = z.infer<typeof userFormSchema>;
export type BulkUserFormData = z.infer<typeof bulkUserFormSchema>;
