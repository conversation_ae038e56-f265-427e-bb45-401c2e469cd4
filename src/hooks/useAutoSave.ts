import { useEffect, useRef, useCallback } from 'react';
import { UseFormGetValues } from 'react-hook-form';
import { BulkUserFormData } from '@/lib/validations';

interface UseAutoSaveOptions<T = BulkUserFormData> {
  key: string;
  getValues: UseFormGetValues<T>;
  enabled?: boolean;
  interval?: number; // 自动保存间隔（毫秒）
  onSave?: (data: T) => void;
  onRestore?: (data: T) => void;
}

interface SavedData<T = BulkUserFormData> {
  data: T;
  timestamp: number;
  version: string;
}

export function useAutoSave<T = BulkUserFormData>({
  key,
  getValues,
  enabled = true,
  interval = 30000, // 默认30秒
  onSave,
  onRestore,
}: UseAutoSaveOptions<T>) {
  const intervalRef = useRef<NodeJS.Timeout>();
  const lastSavedRef = useRef<string>('');

  // 保存数据到 localStorage
  const saveData = useCallback(() => {
    if (!enabled) return;

    try {
      const currentData = getValues();
      const dataString = JSON.stringify(currentData);
      
      // 避免重复保存相同数据
      if (dataString === lastSavedRef.current) return;

      const savedData: SavedData<T> = {
        data: currentData,
        timestamp: Date.now(),
        version: '1.0',
      };

      localStorage.setItem(`autosave_${key}`, JSON.stringify(savedData));
      lastSavedRef.current = dataString;
      
      onSave?.(currentData);
      
      console.log('表单数据已自动保存');
    } catch (error) {
      console.error('自动保存失败:', error);
    }
  }, [key, getValues, enabled, onSave]);

  // 从 localStorage 恢复数据
  const restoreData = useCallback((): SavedData<T> | null => {
    try {
      const saved = localStorage.getItem(`autosave_${key}`);
      if (!saved) return null;

      const savedData: SavedData<T> = JSON.parse(saved);
      
      // 检查数据是否过期（7天）
      const isExpired = Date.now() - savedData.timestamp > 7 * 24 * 60 * 60 * 1000;
      if (isExpired) {
        clearSavedData();
        return null;
      }

      return savedData;
    } catch (error) {
      console.error('恢复数据失败:', error);
      return null;
    }
  }, [key]);

  // 清除保存的数据
  const clearSavedData = useCallback(() => {
    try {
      localStorage.removeItem(`autosave_${key}`);
      lastSavedRef.current = '';
      console.log('已清除自动保存的数据');
    } catch (error) {
      console.error('清除数据失败:', error);
    }
  }, [key]);

  // 检查是否有保存的数据
  const hasSavedData = useCallback((): boolean => {
    return restoreData() !== null;
  }, [restoreData]);

  // 获取保存数据的时间
  const getSavedDataInfo = useCallback(() => {
    const savedData = restoreData();
    if (!savedData) return null;

    return {
      timestamp: savedData.timestamp,
      timeAgo: formatTimeAgo(savedData.timestamp),
      userCount: savedData.data.users.length,
    };
  }, [restoreData]);

  // 手动触发保存
  const manualSave = useCallback(() => {
    saveData();
  }, [saveData]);

  // 恢复数据并触发回调
  const restoreAndApply = useCallback((): T | null => {
    const savedData = restoreData();
    if (savedData) {
      onRestore?.(savedData.data);
      return savedData.data;
    }
    return null;
  }, [restoreData, onRestore]);

  // 设置自动保存定时器
  useEffect(() => {
    if (!enabled) return;

    intervalRef.current = setInterval(saveData, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [saveData, interval, enabled]);

  // 页面卸载时保存
  useEffect(() => {
    if (!enabled) return;

    const handleBeforeUnload = () => {
      saveData();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [saveData, enabled]);

  return {
    saveData: manualSave,
    restoreData: restoreAndApply,
    clearSavedData,
    hasSavedData,
    getSavedDataInfo,
  };
}

// 格式化时间差
function formatTimeAgo(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days > 0) {
    return `${days}天前`;
  } else if (hours > 0) {
    return `${hours}小时前`;
  } else if (minutes > 0) {
    return `${minutes}分钟前`;
  } else {
    return '刚刚';
  }
}
